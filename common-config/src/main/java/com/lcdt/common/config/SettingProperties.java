package com.lcdt.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023-07-11
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "setting")
public class SettingProperties {
    private static final long serialVersionUID = -6690973124095133358L;

    private Long carrierId;

    /**
     * 公章图片地址
     */
    private String gongZhang;

    /**
     * 上传平台名称
     */
    private String uploadPlatform;

    /**
     * 平台二维码
     */
    private String qrCode;

    /**
     * 天津网络货运systemId
     */
    private String tjSystemId;

    /**
     * 天津网络货运signKey
     */
    private String tjSignKey;

    /**
     * 网络货运企业唯一标识appId
     */
    private String upAppId;

    /**
     * 网络货运企业在省平台申请的接入安全码
     */
    private String upAppSecurity;

    /**
     * 网络货运企业在省平台申请的企业发送代码
     */
    private String upEnterpriseSenderCode;

    private String khyUrl;

    private String khyUploadUrl;

    private String khyAppid;

    private String khySecret;

    /**
     * 数据统计接收人手机号，逗号分隔
     */
    private String reportReciver;

    private String specialAreas;


    //黑名单，不传输数据
    private String blacklist;

    private String ahUpAppId;

    private String ahUpAppSecurity;

    private String ahUpEnterpriseSenderCode;


}
