package com.lcdt.common.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.context.annotation.Bean;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021-08-11
 */
//@Configuration
public class RedissonConfig {

//    @Value("${spring.redis.host:127.0.0.1}")
//    private String host;
//
//
//    @Value("${spring.redis.password:#{null}}")
//    private String password;


    @Bean(destroyMethod = "shutdown")
    public RedissonClient redisson() throws IOException {
        //1、创建配置
        Config config = new Config();
        config.useSingleServer();
//                .setPassword("");
        return Redisson.create(config);
    }
}
