package com.lcdt.common.component;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2022-09-08
 */
@Component
@ControllerAdvice
@Slf4j
public class RestExceptionHandler {


    @ExceptionHandler(Exception.class)
    @ResponseBody
    public JSONObject defaultErrorHandler(HttpServletRequest request, Exception e) {
        log.error(e.getMessage(), e);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", -1);
        String message = null;
        if (e.getMessage() == null) {
            message = "空指针异常";
        } else if (e.getMessage().indexOf("Exception:") > 0) {
            int begin = e.getMessage().indexOf(":");
            int end = e.getMessage().indexOf("\n");
            message = e.getMessage().substring(begin + 1, end);
        } else {
            message = e.getMessage();
        }
        jsonObject.put("message", message);
        return jsonObject;
    }
}
