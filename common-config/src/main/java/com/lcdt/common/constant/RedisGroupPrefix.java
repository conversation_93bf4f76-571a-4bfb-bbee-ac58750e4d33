package com.lcdt.common.constant;

/**
 * redis分组 常量
 *
 * <AUTHOR>
 * @date 2021-08-24
 */
public class RedisGroupPrefix {

    /**
     * 移动端登录key
     */
    public static final String APP_LOGIN_KEY = "app_login_key:";

    /**
     * 短信登录验证码
     */
    public static final String LOGIN_SMS_CODE = "login_sms_code:";

    /**
     * 短信验证码
     */
    public static final String SMS_CODE = "sms_code:";
    /**
     * 验证码计数
     */
    public static final String SMS_CODE_RETRY = "sms_code_retry:";
    /**
     * 找回密码 （一次验证码重试次数，默认三次）
     */
    public static final String FIND_PASSWORD_RETRY = "FIND_PASSWORD_RETRY";

    /**
     * 运营端公司id
     */
    public static final String CARRIER_ID = "carrier_id:";

    /**
     * 运营端 merchantId
     */
    public static final String CARRIER_MERCHANT_ID = "carrier_merchant_id:";

    /**
     * 支付加锁
     */
    public static final String DRIVER_BILL_PAY = "driver_bill_pay:";

    /**
     * 提现锁
     */
    public static final String DRIVER_DRAW_LOCK = "driver_draw_lock:";



    /**
     * 分账时添加，主要用于分账通知后的类型区分和部分参数传递
     */
    public static final String ORDER_SHARE_JSON = "order_share_json:";

    /**
     * 电子凭证申请
     */
    public static final String ELECTRONIC_RECEIPT_APPLY = "electronic_receipt_apply:";


    public static final String APP_RATE_LIMIT = "app_rate_limit:";

    /**
     * 有效的计划id
     */
    public static String EFFECTIVE_PLAN_SUFFIX = "EFFECTIVE_PLAN:";
    /**
     * 固定计划的剩余数量
     */
    public static String PLAN_AMOUT_SUFFIX = "PLAN_AMOUNT:";
    /**
     * 有效的固定计划id
     */
    public static String SYSRATES_SUFFIX = "SYSRATES:";
    /**
     * 运单单号生成
     */
    public static String WAYBILL_CODE_SUFFIX = "WAYBILL_CODE:";
    /**
     * 计划单号生成
     */
    public static String PLAN_CODE_SUFFIX = "PLAN_CODE:";
    /**
     * 运单二维码地址
     */
    public static String PIC_PLAN_SUFFIX = "PIC_PLAN:";
    /**
     * 支付前缀
     */
    public static String PAY_MESSAGE_SUFFIX = "PAY_MESSAGE:";
    /**
     * 对账单导入前缀
     */
    public static String COMPANY_BILL_SUFFIX = "COMPANY_BILL:";
    /**
     * 票号导入前缀
     */
    public static String ECT_IMPORT_SUFFIX = "ECT_IMPORT:";
    /**
     * 农行账簿号生成
     */
    public static String ABC_NO = "ABC_NO:";
    /**
     * 农行账簿号生成加锁
     */
    public static String ABC_NO_LOCK = "ABC_NO_LOCK:";
    /**
     * 企业账簿余额（包含运营端账户余额，结算户的）
     */
    public static String COMPANY_BALANCE = "COMPANY_BALANCE:";
    /**
     * 农行支付-余额锁 (涉及余额计算的都必须加锁)
     */
    public static String ABC_BALANCE_LOCK = "ABC_BALANCE_LOCK:";
    /**
     * 更新充值流水锁
     */
    public static String ABC_ACCOUNT_INFO = "ABC_ACCOUNT_INFO:";

    /**
     * 添加员工验证码请求重试次数
     */
    public static final String ADD_SUB_USER_RETRY = "ADD_SUB_USER_RETRY:";

    /**
     * 车辆定位信息
     */
    public static final String GPS_TRACK = "GPS_TRACK:";

    /**
     * 支付加锁
     */
    public static final String COMPILE_STATUS = "COMPILE_STATUS:";

    /**
     * 垫付单号生成
     */
    public static String ADVANCE_CODE_SUFFIX = "ADVANCE_CODE:";

    /**
     * 货主邀请二维码
     */
    public static String COMPANY_INVITE_DRIVER_PIC = "COMPANY_INVITE_DRIVER_PIC:";

    /**
     * 绑定提现卡redis
     */
    public static String BIN_CARD_DTO = "BIN_CARD_DTO";
}
