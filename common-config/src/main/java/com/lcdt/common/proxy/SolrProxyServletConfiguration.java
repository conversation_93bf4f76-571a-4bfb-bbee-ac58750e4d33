package com.lcdt.common.proxy;

import org.mitre.dsmiley.httpproxy.ProxyServlet;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SolrProxyServletConfiguration {

    // 读取配置文件中路由设置
    private final static String[] SERVLET_URL = {"/oss/proxy/"};

    @Bean
    public ServletRegistrationBean servletRegistrationBean() {
        ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean(new MyProxyServlet(), SERVLET_URL);
        servletRegistrationBean.addInitParameter(ProxyServlet.P_TARGET_URI, "");
        // 是否打印日志
//        servletRegistrationBean.addInitParameter(ProxyServlet.P_LOG, "true");
        return servletRegistrationBean;
    }


}
