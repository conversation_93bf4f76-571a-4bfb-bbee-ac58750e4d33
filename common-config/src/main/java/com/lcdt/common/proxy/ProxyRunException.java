package com.lcdt.common.proxy;

public class ProxyRunException extends RuntimeException {

    private Integer code;

    public ProxyRunException(String message) {
        super(message);
        this.code = -1;
    }

    public ProxyRunException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

}
