package com.lcdt.common.prop;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-05-10
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "abc")
public class AbcProperties implements Serializable {
    private long serialVersionUID = -791883597620096567L;

    /**
     * 前置机 ip
     */
    private String serverIp;

    /**
     * 前置机
     */
    private int serverPort;

    /**
     * 客户号
     */
    private String corpNo;

    /**
     * 平台户名
     */
    private String bankName;

    /**
     *  (镜湖)平台户名
     */
    private String ahBankName;

    /**
     * 监管户账号 （实际卡号前面去掉了15）
     */
    private String accountNo;

    /**
     * (镜湖)监管户账号 （实际卡号前面去掉了15）
     */
    private String ahAccountNo;

    /**
     * 监管户联行号
     */
    private String bankNo;

    /**
     * 结算户（实际卡号前面去掉了15）
     */
    private String settlementAccountNo;

    /**
     *  (镜湖)结算户（实际卡号前面去掉了15）
     */
    private String ahSettlementAccountNo;

    /**
     * 结算户公户账簿号（会体现在电子回单上面）
     */
    private String settlementAbNo;

    /**
     * 结算户联行号
     */
    private String settlementBankNo;

    /**
     * 手续费账簿
     */
    private String chargeAbNo;

    /**
     * 省市代码 15
     */
    private String prov;

    /**
     * 卡号前缀（监管户/结算户）
     */
    private String accountNoPre;

}
