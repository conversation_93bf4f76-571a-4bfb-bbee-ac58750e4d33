<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>clms-user</artifactId>
        <groupId>com.lcdt.cloud</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <packaging>jar</packaging>
    <artifactId>clms-user-service</artifactId>
    <name>clms-user-service</name>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
            <version>1.7</version>
        </dependency>
<!--        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-notify-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-response-converter</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>common-utils</artifactId>
            <version>1.0</version>
        </dependency>

        <!--        权限验证模块-->
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-security</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-user-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
        </dependency>


        <!--oss依赖-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-traffic-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-payment-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.8</version>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-platform-api</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-dependencies</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>