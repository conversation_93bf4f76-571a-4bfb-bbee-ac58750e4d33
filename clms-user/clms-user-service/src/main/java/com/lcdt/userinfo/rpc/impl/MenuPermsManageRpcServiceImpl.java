package com.lcdt.userinfo.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lcdt.security.exception.CustomException;
import com.lcdt.userinfo.dao.MenuMapper;
import com.lcdt.userinfo.dto.MenuDto;
import com.lcdt.userinfo.model.Menu;
import com.lcdt.userinfo.rpc.MenuPermsManageRpcService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MenuPermsManageRpcServiceImpl implements MenuPermsManageRpcService {

    @Autowired
    private MenuMapper menuMapper;

    /**
     * 递归获取菜单权限列表
     *
     * @param menuPid
     * @param sysType
     * @return
     */
    @Override
    public List<MenuDto> menuList(Long menuPid, Integer sysType) {
        return menuMapper.selectByPid(menuPid, sysType);
    }

    /**
     * 删除菜单操作
     *
     * @param menuId
     * @return
     */
    @Override
    public int delMenu(Long menuId) {
        // 先判断该菜单下是否存在子菜单，如果存在则无法删除
        Long count = menuMapper.selectCount(new QueryWrapper<Menu>().lambda()
                .eq(Menu::getMenuPid, menuId));
        if (count > 0) {
            throw new CustomException("该菜单下面存在子菜单，无法删除");
        }
        return menuMapper.deleteById(menuId);
    }

    /**
     * 新增菜单（and权限）
     *
     * @param menu
     * @return
     */
    @Override
    public int addMenu(Menu menu) {
        // 先判断该菜单是否重名、menu_code是否重复
        menuExit(menu);
        return menuMapper.insert(menu);
    }

    /**
     * 更新菜单（and权限）
     *
     * @param menu
     * @return
     */
    @Override
    public int modMenu(Menu menu) {
        // 先判断该菜单是否重名、menu_code是否重复
        Long count = menuMapper.selectCount(new QueryWrapper<Menu>().lambda()
                .eq(Menu::getSysType, menu.getSysType())
                .ne(Menu::getMenuId, menu.getMenuId())
                .nested(i -> i.eq(Menu::getMenuName, menu.getMenuName())
                        .or()
                        .eq(Menu::getMenuCode, menu.getMenuCode())));
        // 大于1标识排除自身的重复检查
        if (count > 0) {
            throw new CustomException("菜单名称重复或菜单编码重复，请检查");
        }
        return menuMapper.updateById(menu);

    }

    private void menuExit(Menu menu) {
        Long count = menuMapper.selectCount(new QueryWrapper<Menu>().lambda()
                .eq(Menu::getSysType, menu.getSysType())
                .nested(i -> i.eq(Menu::getMenuName, menu.getMenuName())
                        .or()
                        .eq(Menu::getMenuCode, menu.getMenuCode())));
        if (count > 0) {
            throw new CustomException("菜单名称重复或菜单编码重复，请检查");
        }
    }
}
