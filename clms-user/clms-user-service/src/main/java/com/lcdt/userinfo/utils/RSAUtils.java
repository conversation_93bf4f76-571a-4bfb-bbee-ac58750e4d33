package com.lcdt.userinfo.utils;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023-04-10
 */
@Slf4j
public class RSAUtils {

    public static final String PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALBiDYJYlrWu7ClhlIIFgp+aFM1mt/fhe+f3n1kpR9nM3GCkOGaO5dl8JruaoE0e1q+eR1iayfekRxEsKkzBnGZJ+lHRz0lW1KshV3cprSOJZvjkfJzdlVzhd4Q/Zxu+bMSt0pgMxbM6s8NsFH1Ith1zjrB3Y2iBVNtNtbJY/X4TAgMBAAECgYBe+v4hUckeXuqnQfexqHv1rAhFr2mjnxWVDWAOqE8btth5ZhVFDPAzFErbPWHTBac//LCYwwm9RAZM9gZ1PNEVz72fbRse0jLehm4DPp1bU0DlM2xGbpKUwzikiqkXC0h0Yv6rQk5s3av6QLggDTA8gnLKdmHdMAiLnBDjYKozGQJBAPx/WLvIOW0TEBJB3Nhku75ZuA3wllzeoat4T8T3vir2Izt+IuehWTaEOSL3RsqQyBfDMxMFFM6aCC8y0K7fwv0CQQCy1GnM3gbmv4TAM2WxUFd7V+/YW2VT7XtbCj1xJTXWB42Njb2KOJ4z+AEZw8IuJZx8b4KT0HiqR+Uy65qZvzpPAkEAuZJGfsxrGDUYbLKnQwrVyNJbqTMW725YV5N23QZPZ3e68TCFKsO0s5J+A6rFBn9d78jwspvbbPRb64RnRv8BaQJALS3G0PAi1Db8ZcZWYbaHc2zi9kkYz1xG9tgeb7BT6o+b7nApu3rUVRLJzGWNcDUmVLWjSo63fWWOfQ/SzhbmtwJAbcFBIL4Rke3XhapJG+vlJITQagyhnwoqF/wO4qok11aOv2aIUigtbBbs0KDrziO3cmDIUM1kst5mZ//bJGLkxA==";
    public static final String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwYg2CWJa1ruwpYZSCBYKfmhTNZrf34Xvn959ZKUfZzNxgpDhmjuXZfCa7mqBNHtavnkdYmsn3pEcRLCpMwZxmSfpR0c9JVtSrIVd3Ka0jiWb45Hyc3ZVc4XeEP2cbvmzErdKYDMWzOrPDbBR9SLYdc46wd2NogVTbTbWyWP1+EwIDAQAB";
    public static final String ENCRYPT_TYPE = "RSA";

    public static RSA getPrivateRSA() {
        return SecureUtil.rsa(PRIVATE_KEY, null);
    }

    /**
     * 公钥加密
     *
     * @param content   要加密的内容
     * @param publicKey 公钥
     */
    public static String encrypt(String content, String publicKey) {
        try {
            RSA rsa = new RSA(null, publicKey);
            return rsa.encryptBase64(content, KeyType.PublicKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 私钥解密
     *
     * @param content    要解密的内容
     * @param privateKey 私钥
     */
    public static String decrypt(String content, String privateKey) {
        try {
            RSA rsa = new RSA(privateKey, null);
            return rsa.decryptStr(content, KeyType.PrivateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 生成公钥私钥
     */
    public static void main(String[] args) {
        try {
//            KeyPair pair = SecureUtil.generateKeyPair(ENCRYPT_TYPE);
//            PrivateKey privateKey = pair.getPrivate();
//            PublicKey publicKey = pair.getPublic();
//            // 获取 公钥和私钥 的 编码格式（通过该 编码格式 可以反过来 生成公钥和私钥对象）
//            byte[] pubEncBytes = publicKey.getEncoded();
//            byte[] priEncBytes = privateKey.getEncoded();
//            // 把公钥和私钥的编码格式转换为Base64文本
//            String pubEncBase64 = Base64.encode(pubEncBytes);
//            String priEncBase64 = Base64.encode(priEncBytes);
//            log.info("公钥: {}", pubEncBase64);
//            log.info("私钥: {}", priEncBase64);

            RSA rsa = SecureUtil.rsa(null, PUBLIC_KEY);
            String pubStr = rsa.encryptBase64("123456", KeyType.PublicKey);
            System.out.println(pubStr);
//            RSA rsa1 = SecureUtil.rsa(privateKey, null);
//            String destr = rsa1.decryptStr(pubStr, KeyType.PrivateKey);
            String pub = "KN+WIOPxLd8QYeEKQh1tLtFCIPNQoM9Bh5aVTKnMB2W61uPoX4DlEhqPNKhPAlseJNVyTwKiYDbWCPy2z3oHQ4eNGzWiMpuW9wHZH4lizv5NNBga39KnpxdlYslYC/CKwTeWtKxyvvEYwcQYojd/U3Lz8oWxRskET3NnjuWwyB5fXbUkJLK1xJWIEJvM3OZJmFo1Q2z7QCHGFjC7bzcp3vd9?ok/nWcuPG85d8Tix9F1kdD6mtfsR4Im5MZBdUjKO43M=";
            String decrypt = decrypt(pub, PRIVATE_KEY);
            System.out.println(decrypt);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
