package com.lcdt.userinfo.rpc.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.userinfo.dao.BalanceRecordMapper;
import com.lcdt.userinfo.dao.CompanyCertificateMapper;
import com.lcdt.userinfo.dao.CompanyMapper;
import com.lcdt.userinfo.dao.CompanyQuotaMapper;
import com.lcdt.userinfo.dto.BalanceManageDto;
import com.lcdt.userinfo.dto.CompanyBalanceDto;
import com.lcdt.userinfo.dto.CompanyQueryDto;
import com.lcdt.userinfo.dto.CompanyResponseDto;
import com.lcdt.userinfo.model.*;
import com.lcdt.userinfo.rpc.CarrierBalanceRpcService;
import com.lcdt.userinfo.rpc.CompanyRpcService;
import com.lcdt.userinfo.service.BalanceRecordService;
import com.lcdt.userinfo.service.CompanyService;
import com.lcdt.userinfo.vo.BalanceAdjustment;
import com.lcdt.util.CheckEmptyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * Created by yangbinq on 2018/1/30.
 */
@Service //(timeout = 5000)
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class CompanyRpcServiceImpl implements CompanyRpcService {

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    public CompanyCertificateMapper certificateDao;

    @Autowired
    private com.lcdt.userinfo.dao.UserMapper userMapper;

    @Autowired
    private BalanceRecordService balanceRecordService;

    @Autowired
    private CarrierBalanceRpcService carrierBalanceRpcService;

    @Autowired
    BalanceRecordMapper balanceRecordMapper;


    @Autowired
    private CompanyService companyService;

    @Autowired
    private CompanyQuotaMapper companyQuotaMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Company findCompanyByCid(Long companyId) {
        return companyMapper.selectById(companyId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public User findCompanyCreate(Long compId) {
        return userMapper.compCreateUserByCompId(compId);
    }


    @Override
    public User findUserByPhone(String phone) {
        return userMapper.queryByUserPhone(phone);
    }

    @Override
    public User selectByPrimaryKey(Long userId) {
        return userMapper.selectByPrimaryKey(userId);
    }


    @Override
    public List<Company> findCompanyByName(String companyName) {
        return companyMapper.selectList(Wrappers.<Company>lambdaQuery().like(Company::getFullName, companyName));
    }

    @Override
    public List<Company> queryCompanyByNameEabled(String companyName) {
        return companyMapper.selectList(Wrappers.<Company>lambdaQuery().like(Company::getFullName, companyName)
                .eq(Company::getEnable, 1));
    }

    @Override
    public IPage<CompanyBalanceDto> getCompanyBalanceList(Page<CompanyBalanceDto> page, CompanyBalanceDto companyBalanceDto) {
        return companyMapper.selectBalance(page, companyBalanceDto);
    }

    /**
     * 原线下管理后台充值---停用
     */
    @Override
    public int balanceChange(BalanceManageDto balanceManageDto) {
        // 分为两步 1、更新公司表关于余额的字段 2、创建余额流水记录
        // 首先查询当前要变更的企业信息
        int rows = 0;
        Company company = companyMapper.selectById(balanceManageDto.getCompanyId());
        // 重新组织一个Company对象用来进行余额相关字段的更新
        Company cy = new Company();
        cy.setCompId(balanceManageDto.getCompanyId());
        cy.setBalanceTotal(company.getBalanceTotal().add(balanceManageDto.getRechargeAmount()));
        // 如果是充值需要设置最后一次充值的金额和最后一次充值的时间,支付运费不需要，只变动余额即可
        if (balanceManageDto.getChangeType().intValue() == 0) {
            cy.setLastRechargeNum(balanceManageDto.getRechargeAmount());
            cy.setLastRechargeTime(new Date());
        }
        cy.setVersion(company.getVersion());
        rows = companyMapper.updateBalance(cy);
        if (rows > 0) {
            // 创建余额流水记录
            BalanceRecord balanceRecord = new BalanceRecord();
            // 设置付款类型
            balanceRecord.setPayType(balanceManageDto.getPayType());
            balanceRecord.setChangeAmount(balanceManageDto.getRechargeAmount());
            balanceRecord.setFinalAmount(cy.getBalanceTotal());
            balanceRecord.setOperator(balanceManageDto.getOperator());
            balanceRecord.setOperatorId(balanceManageDto.getOperatorId());
            balanceRecord.setCreateTime(new Date());
            // 变动类型为 0-充值 1-支付运费
            balanceRecord.setChangeType(balanceManageDto.getChangeType());
            balanceRecord.setRemark(balanceManageDto.getRemark());
            balanceRecord.setCompanyId(balanceManageDto.getCompanyId());
            balanceRecord.setRelateOrderNo(balanceManageDto.getRelateOrderNo());
            rows += balanceRecordService.saveBalanceRecord(balanceRecord);
        }
        return rows;
    }


    @Override
    public CompanyCertificate getCompanyCert(Long companyId) {
        List<CompanyCertificate> companyCertificate = certificateDao.selectByCompanyId(companyId);
        if (companyCertificate != null && !companyCertificate.isEmpty()) {
            CompanyCertificate result = companyCertificate.get(0);
            //法人手机号设置为托运人的管理员的手机号
            String s = companyMapper.selectCompanyPhone(companyId);
            if (CheckEmptyUtil.isNotEmpty(s)) {
                result.setIdentityPhone(s);
            }
            return result;
        } else {
            return null;
        }
    }

    @Override
    public BalanceRecord queryByOrderNo(String relateOrderNo) {
        return balanceRecordMapper.selectOne(new QueryWrapper<BalanceRecord>().lambda().eq(BalanceRecord::getRelateOrderNo, relateOrderNo));
    }

    @Override
    public PageInfo queryAllConpany(CompanyQueryDto dto) {
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        return new PageInfo(companyMapper.selectByCompanyDto(dto));
    }

    @Override
    public Company queryCarrierInfo() {
        return companyMapper.selectOne(new QueryWrapper<Company>().lambda()
                .eq(Company::getCompanyType, 2));
    }

    @Override
    public List<Company> queryShipperList(String shipperName) {
        return companyMapper.selectList(new QueryWrapper<Company>().lambda()
                .select(Company::getCompId, Company::getFullName, Company::getShortName)
                .like(Company::getFullName, shipperName)
                .eq(Company::getAuthentication, 2)
                .eq(Company::getCompanyType, 1));
    }

    @Override
    public List<Company> queryByIds(List<Long> ids) {
        return companyMapper.selectList(new QueryWrapper<Company>().lambda()
                .select(Company::getCompId, Company::getFullName, Company::getShortName)
                .eq(Company::getAuthentication, 2)
                .eq(Company::getCompanyType, 1)
                .in(Company::getCompId, ids));
    }


    @Override
    public int updateShipperCashTask() {
        companyService.updateShipperCashTask();
        return 0;
    }

    @Override
    public AbcAccount updateAccount(Long compId) {
        return companyService.updateAccount(compId);
    }

    @Override
    public JSONObject getAccount(Company company) {
        return companyService.getAccount(company);
    }

    @Override
    public Company selectById(Long carrierCompanyId) {
        return companyService.selectById(carrierCompanyId);
    }

    @Override
    public Company selectAbNo(Long companyId) {
        return companyMapper.selectOne(new QueryWrapper<Company>().lambda()
                .select(Company::getAbNo)
                .eq(Company::getCompId, companyId));
    }

    @Override
    public JSONObject getCarrierBalanceFromLastRecord(Company carrierCompany, String affiliatedPlatform) {
        return companyService.getCarrierBalanceFromLastRecord(carrierCompany, affiliatedPlatform);
    }

    @Override
    public void updateCompanyCertInfo(CompanyCertificate companyCert) {
        companyService.updateCompanyCertInfo(companyCert);
    }

    @Override
    public void updateAccountBookInfo(Long compId, String startDate, String endDate) throws Exception {
        companyService.updateAccountBookInfo(compId, startDate, endDate);
    }

    @Override
    public void updateCarrierCashFlow(Long compId, String date) throws Exception {
        companyService.updateCarrierCashFlow(compId, date);
    }

    @Override
    public void balanceAdjustment(Long compId, BalanceAdjustment balanceAdjustment, User userInfo) {
        companyService.balanceAdjustment(compId, balanceAdjustment, userInfo);
    }

    @Override
    public JSONObject getPremium(String affiliatedPlatform, String abNo) {
        return companyService.getPremium(affiliatedPlatform, abNo);
    }

    @Override
    public AbcAccount getCarrierAccountFromAbcApi(String affiliatedPlatform, Long compId) {
        return companyService.getCarrierAccountFromAbcApi(affiliatedPlatform, compId);
    }


    @Override
    public PageInfo<CompanyResponseDto> queryKhyCompanyList(CompanyQueryDto dto) {
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        return new PageInfo(companyMapper.queryDriverListByKhy(dto));
    }

    @Override
    public CompanyQuota findCompanyQuotaByCompId(Long compId) {
        LambdaQueryWrapper<CompanyQuota> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CompanyQuota::getCompId, compId);
        lambdaQueryWrapper.eq(CompanyQuota::getEnable, 1);
        List<CompanyQuota> companyQuotas = companyQuotaMapper.selectList(lambdaQueryWrapper);
        if (CheckEmptyUtil.isNotEmpty(companyQuotas)) {
            return companyQuotas.get(0);
        } else {
            return null;
        }

    }

    @Override
    public Company queryCompanyByUserId(Long userId) {
        return companyMapper.queryCompanyByUserId(userId);
    }

    @Override
    public List<Company> findCompanyByLinkTel(String username) {
        return companyService.findCompanyByLinkTel(username);
    }

    @Override
    public void setPayPassword(Long compId, String payPassword, String affiliatedPlatform) {
        companyService.setPayPassword(compId, payPassword, affiliatedPlatform);
    }

    @Override
    public void reconciliation(ReconciliationRecord reconciliationRecord) {
        companyService.reconciliation(reconciliationRecord);
    }

}
