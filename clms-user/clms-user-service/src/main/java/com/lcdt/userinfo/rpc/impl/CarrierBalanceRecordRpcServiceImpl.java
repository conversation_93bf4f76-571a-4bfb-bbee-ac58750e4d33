package com.lcdt.userinfo.rpc.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lcdt.userinfo.dao.CarrierBalanceRecordMapper;
import com.lcdt.userinfo.dao.CompanyMapper;
import com.lcdt.userinfo.model.CarrierBalanceRecord;
import com.lcdt.userinfo.rpc.CarrierBalanceRpcService;
import com.lcdt.userinfo.service.CarrierBalanceService;
import com.lcdt.util.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class CarrierBalanceRecordRpcServiceImpl implements CarrierBalanceRpcService {

    @Autowired
    private CarrierBalanceService carrierBalanceService;

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private CarrierBalanceRecordMapper carrierBalanceRecordMapper;


    @Override
    public String getElectronicReceipt(String waybillCode) {
        Map<String, Object> stringObjectMap = carrierBalanceRecordMapper.queryByWaybillCode(waybillCode);
        if (!ObjectUtils.isEmpty(stringObjectMap)) {
            return stringObjectMap.get("oss_url") != null ? stringObjectMap.get("oss_url").toString() : "";
        }
        return "";
    }

    @Override
    public List<CarrierBalanceRecord> get3DayData() {
        return carrierBalanceRecordMapper.selectList(new QueryWrapper<CarrierBalanceRecord>().lambda()
                .ge(CarrierBalanceRecord::getRecordTime, DateUtils.nextDay(-3))
                .in(CarrierBalanceRecord::getBankStatementType, 1, 2, 3)
                .notInSql(CarrierBalanceRecord::getRelateOrderNo, "select order_no from uc_electronic_receipt"));
    }

    @Override
    public int historyDataHandler(Long recordId) {
        int rows = 0;
        List<CarrierBalanceRecord> carrierBalanceRecordList = carrierBalanceRecordMapper.selectList(new QueryWrapper<CarrierBalanceRecord>().lambda()
                .select(CarrierBalanceRecord::getRecordId,
                        CarrierBalanceRecord::getBankStatementType,
                        CarrierBalanceRecord::getBankStatementAmount,
                        CarrierBalanceRecord::getBankStatementCurrentAmount)
                .ge(CarrierBalanceRecord::getRecordId, recordId)
                .ne(CarrierBalanceRecord::getBankStatementType, 5)
                .orderByAsc(CarrierBalanceRecord::getRecordId));
        CarrierBalanceRecord updateRecord = new CarrierBalanceRecord();
        System.out.println("----------开始处理余额错误记录----------");
        for (int i = 1; i < carrierBalanceRecordList.size(); i++) {
            synchronized (this) {
                BigDecimal updateAmount, updateCurrentAmount;
                CarrierBalanceRecord current = carrierBalanceRecordList.get(i);
                // 计算出变动金额，这个是正确的 可以是负值，表示运费支出
                BigDecimal temp = current.getBankStatementCurrentAmount().subtract(current.getBankStatementAmount());
                // 获取前一笔交易的数据
                CarrierBalanceRecord pre = carrierBalanceRecordList.get(i - 1);
                // 本条原金额就是上一条记录的变动后的金额
                updateAmount = pre.getBankStatementCurrentAmount();
                // 计算当前记录的变动后金额  原始金额 + 变动金额 = 变动后的余额
                updateCurrentAmount = updateAmount.add(temp);
                // 设置主键
                updateRecord.setRecordId(current.getRecordId());
                // 当前记录的原金额 = 上一调记录的变动后金额
                updateRecord.setBankStatementAmount(updateAmount);
                updateRecord.setBankStatementCurrentAmount(updateCurrentAmount);
                rows += carrierBalanceRecordMapper.updateById(updateRecord);
                System.out.println("recordId:" + current.getRecordId() + " amount:" + updateAmount + " currentAmount:" + updateCurrentAmount);
                // 跟新之后变更List里面的数据
                current.setBankStatementAmount(updateAmount);
                current.setBankStatementCurrentAmount(updateCurrentAmount);
            }
        }
        return rows;
    }

    @Override
    public Map<String, Object> queryReceiptNoByWaybillCode(String waybillCode) {
        Map<String, Object> stringObjectMap = carrierBalanceRecordMapper.queryReceiptNoByWaybillCode(waybillCode);
        return stringObjectMap;
    }

    @Override
    public int save(CarrierBalanceRecord carrierBalanceRecord) {
        return carrierBalanceRecordMapper.insert(carrierBalanceRecord);
    }


    public static void main(String[] args) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String defaultStartDate = sdf.format(DateUtils.nextDay(-3));
        System.out.println(defaultStartDate);
    }

    @Override
    public Map<String, Integer> payStatistics() {
        Map<String, Integer> map = carrierBalanceRecordMapper.payStatistics();
        if (ObjectUtil.isEmpty(map)) {
            map = new HashMap<>();
            map.put("payment_income", 0);
            map.put("payment_outlay", 0);
        }
        Map<String, Integer> stringIntegerMap = carrierBalanceRecordMapper.payStatisticsAh();
        if (ObjectUtil.isNotEmpty(stringIntegerMap)) {
            map.putAll(stringIntegerMap);
        }
        return map;
    }


}
