package com.lcdt.userinfo.web.controller.api;

import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.userinfo.service.LoginLogService;
import com.lcdt.userinfo.web.dto.PageResultDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import java.beans.PropertyEditorSupport;
import java.util.Date;

@RestController
@RequestMapping("/api/logs")
/**
 * 登录日志接口
 */
public class LoginLogApi {

    @Autowired
    private LoginLogService loginLogService;

    @Autowired
    SecurityInfoGetter securityInfoGetter;

    /**
     * 登录日志列表
     *
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param userName 用户名
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return 登录日志列表
     */
    @GetMapping("/list")
    /**
     * 登录日志列表
     *
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param userName 用户名
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return 登录日志列表
     */
    public PageResultDto companyUserLogs(
            @RequestParam(required = false) Date beginTime,
            @RequestParam(required = false) Date endTime,
            @RequestParam(required = false) String userName,
            @RequestParam Integer pageNo,
            @RequestParam Integer pageSize){
        Long companyId = securityInfoGetter.getCompId();
        return new PageResultDto(loginLogService.loginLogList(companyId,userName,null,beginTime,endTime,pageNo,pageSize).getList());
    }

    @InitBinder
    public void initBinder(final WebDataBinder webdataBinder) {
        webdataBinder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) throws IllegalArgumentException {
                if (StringUtils.isEmpty(text)) {
                    setValue(null);
                    return;
                }
                setValue(new Date(Long.valueOf(text)));
            }
        });
    }
}
