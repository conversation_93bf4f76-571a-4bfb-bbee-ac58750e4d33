package com.lcdt.userinfo.rpc.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.traffic.service.WaybillRpcService;
import com.lcdt.userinfo.dao.DataReportingLogMapper;
import com.lcdt.userinfo.dto.DataReportingLogDto;
import com.lcdt.userinfo.model.DataReportingLog;
import com.lcdt.userinfo.rpc.DataReportingLogRpcService;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;

@Service
@Transactional
@Slf4j
public class DataReportingLogRpcServiceImpl implements DataReportingLogRpcService {
    @Autowired
    DataReportingLogMapper dataReportingLogMapper;

    @Autowired
    WaybillRpcService waybillRpcService;

    @Override
    public PageInfo queryList(DataReportingLogDto dataReportingLogDto) {
        PageHelper.startPage(dataReportingLogDto.getPageNo(), dataReportingLogDto.getPageSize());
        List<DataReportingLog> dataReportingLogList = dataReportingLogMapper.queryList(dataReportingLogDto);
        return new PageInfo(dataReportingLogList);
    }

}
