package com.lcdt.userinfo.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.lcdt.common.config.AliyunOssConfig;
import com.lcdt.userinfo.service.OperationLogService;
import com.lcdt.userinfo.utils.JSONResponseUtil;
import com.lcdt.userinfo.utils.ResponseMessage;
import com.lcdt.util.FileNameUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Created by ybq on 2020/5/1 14:51
 */
/**
 * OSS文件上传控制器
 */
@RestController
@RequestMapping("/oss")
public class OssUploadController {

    @Autowired
    OperationLogService operationLogService;

    @Autowired
    private AliyunOssConfig aliyunOssConfig;

    @Value("${isDebug}")
    private boolean isDebug;

    /**
     * 上传图片
     * 只用于司机车辆图片上传
     *
     * @param file 文件
     * @param fileType 文件类型
     * @return 上传结果
     * @throws IOException IO异常
     */
    @PostMapping("/upload/images")
    public ResponseMessage imageUpload(@RequestParam(value = "file") MultipartFile file, @RequestParam(value = "fileType") String fileType) throws IOException {
        if (file.isEmpty()) {
            return JSONResponseUtil.failure("上传文件出错!!!", -1);
        }
        String fileName = file.getOriginalFilename();
        String ext = getExtensionName(fileName);
//        String newFileName = AppUtility.generateId_String_20() + "." + ext;
        String newFileName = FileNameUtil.generateMsRandomName() + "." + ext;
        if (!ext.toLowerCase().equals("jpg") && !ext.toLowerCase().equals("jpeg") && !ext.toLowerCase().equals("png")) {
            return JSONResponseUtil.failure("上传文件类型错误,支持类型：jpg/png!", -1);
        }
        InputStream inputStream = file.getInputStream();

        if (file.getSize() >= 1 * 1024 * 1024) {
            // 把图片读入到内存中
            BufferedImage bufImg = ImageIO.read(inputStream);
            // 压缩代码
            // 存储图片文件byte数组
            ByteArrayOutputStream bos = new ByteArrayOutputStream();

            //防止图片变红
            BufferedImage newBufferedImage = new BufferedImage(bufImg.getWidth() - 100, bufImg.getHeight() - 100,
                    BufferedImage.TYPE_INT_RGB);
            newBufferedImage.createGraphics().drawImage(bufImg, 0, 0, Color.WHITE, null);
            //先转成jpg格式来压缩,然后在通过OSS来修改成源文件本来的后缀格式
            ImageIO.write(newBufferedImage, "jpg", bos);
            //获取输出流
            inputStream = new ByteArrayInputStream(bos.toByteArray());

            bufImg.getGraphics().dispose(); //释放
        }

        OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(),
                aliyunOssConfig.getAccessId(),
                aliyunOssConfig.getAccessKey());

        if (fileType.equals("") || fileType.equals(null)) {
            fileType = "api";
        }
        if (isDebug) {
            fileType = "common/" + fileType + "_test";
        } else {
            fileType = "common/" + fileType;
        }
        ossClient.putObject(aliyunOssConfig.getBucket(), fileType + "/" + newFileName, inputStream); //aliyunOssConfig.getDir()
        ossClient.shutdown();
        return JSONResponseUtil.success(aliyunOssConfig.getHost() + "/" + fileType + "/" + newFileName);
    }


    /**
     * 上传文件
     *
     * @param file 文件
     * @param fileType 文件类型
     * @return 上传结果
     * @throws IOException IO异常
     */
    @PostMapping("/upload/file")
    public ResponseMessage fileUpload(@RequestParam(value = "file") MultipartFile file, @RequestParam(value = "fileType") String fileType) throws IOException {
        if (file.isEmpty()) {
            return JSONResponseUtil.failure("上传文件出错!!!", -1);
        }
        String fileName = file.getOriginalFilename();
        String ext = getExtensionName(fileName);
//        String newFileName = AppUtility.generateId_String_20() + "." + ext;
        String newFileName = FileNameUtil.generateMsRandomName() + "." + ext;
        if (!ext.toLowerCase().equals("jpg") && !ext.toLowerCase().equals("jpeg") && !ext.toLowerCase().equals("png") && !ext.toLowerCase().equals("pdf") && !ext.toLowerCase().equals("bmp")) {
            return JSONResponseUtil.failure("上传文件类型错误,支持类型：jpg/png/bmp/pdf!", -1);
        }

        OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(),
                aliyunOssConfig.getAccessId(),
                aliyunOssConfig.getAccessKey());

        if (isDebug) {
            fileType = fileType + "_test";
        } else {
            fileType = fileType;
        }
        ossClient.putObject(aliyunOssConfig.getBucket(), fileType + "/" + newFileName, file.getInputStream()); //aliyunOssConfig.getDir()
        ossClient.shutdown();

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("url", aliyunOssConfig.getHost() + "/" + fileType + "/" + newFileName);
        jsonObject.put("name", fileName);
        return JSONResponseUtil.success(jsonObject);
    }


    /**
     * 删除文件
     *
     * @param file 文件名
     * @param fileType 文件类型
     * @return 删除结果
     */
    @PostMapping(value = "/delete")
    public ResponseMessage deleteImage(@RequestParam(value = "file") String file, @RequestParam(value = "fileType") String fileType) {
        if (fileType.equals("") || fileType.equals(null)) {
            fileType = "api";
        }
        OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(),
                aliyunOssConfig.getAccessId(),
                aliyunOssConfig.getAccessKey());
        if (isDebug) {
            fileType = fileType + "_test";
        } else {
            fileType = fileType;
        }
        ossClient.deleteObject(aliyunOssConfig.getBucket(), fileType + "/" + file);
        ossClient.shutdown();
        operationLogService.addOperationLog("合同删除");
        return JSONResponseUtil.success("ok");
    }


    /*
     * Java文件操作 获取文件扩展名
     */
    public static String getExtensionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1);
            }
        }
        return filename;
    }

    /*
     * Java文件操作 获取不带扩展名的文件名
     */
    public static String getFileNameNoEx(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length()))) {
                return filename.substring(0, dot);
            }
        }
        return filename;
    }
}