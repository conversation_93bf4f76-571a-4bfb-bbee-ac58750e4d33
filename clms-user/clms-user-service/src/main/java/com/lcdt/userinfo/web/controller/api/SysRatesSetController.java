package com.lcdt.userinfo.web.controller.api;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.utils.StringUtils;
import com.lcdt.common.constant.RedisGroupPrefix;
import com.lcdt.notify.rpcservice.ValidCodeService;
import com.lcdt.userinfo.dao.SysRatesSetMapper;
import com.lcdt.userinfo.dto.SysRatesSetDto;
import com.lcdt.userinfo.model.SysRatesSet;
import com.lcdt.userinfo.rpc.SysRatesSetService;
import com.lcdt.userinfo.utils.JSONResponseUtil;
import com.lcdt.userinfo.utils.ResponseMessage;
import com.lcdt.userinfo.web.dto.PageBaseDto;
import com.lcdt.util.CheckEmptyUtil;
import com.lcdt.util.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
/**
 * 服务费参数管理控制器
 */
@RestController
@RequestMapping("/sysrates")
public class SysRatesSetController {

    @Autowired
    SysRatesSetMapper sysRatesSetMapper;

    @Autowired
    ValidCodeService validCodeService;

    @Autowired
    SecurityInfoGetter securityInfoGetter;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private SysRatesSetService sysRatesSetService;

    private String validcodeTag = "sysrates";

    /**
     * 服务费列表
     *
     * @param companyName 公司名称
     * @param type 类型
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return 服务费列表
     */
    @GetMapping("/list")
    public ResponseMessage list(String companyName, Integer type, Long pageNo, Long pageSize) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if (!StringUtils.isEmpty(companyName)) {
            queryWrapper.like("rates_company_name", companyName);
        }
        if (type != null) {
            queryWrapper.eq("rates_type", type);
        }
        IPage<SysRatesSet> iPage = sysRatesSetMapper.selectPage(new Page<>(pageNo, pageSize), queryWrapper);
        return JSONResponseUtil.success(new PageBaseDto(iPage.getRecords(), iPage.getTotal()));
    }

    /**
     * 费率设置
     *
     * @param sysRatesSetDto 费率设置信息
     * @return 设置结果
     */
    @PostMapping(value = "/ratesupdate")
    @ResponseBody
    public ResponseMessage update(SysRatesSetDto sysRatesSetDto) {
        UpdateWrapper<SysRatesSet> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("rates_id", sysRatesSetDto.getRatesId());
        updateWrapper.set("rates_type", sysRatesSetDto.getRatesType());
        updateWrapper.set("rates_frist", sysRatesSetDto.getRatesFrist());
        if (sysRatesSetDto.getRatesType() == 2) {
            //按计划类型
            updateWrapper.set("rates_second", sysRatesSetDto.getRatesSecond());
        }
        updateWrapper.set("update_time", new Date());
        int rows = sysRatesSetMapper.update(new SysRatesSet(), updateWrapper);
        //更新redis里面的数据
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like("rates_id", sysRatesSetDto.getRatesId());
        List<SysRatesSet> list = sysRatesSetMapper.selectList(queryWrapper);
        SysRatesSet sysRatesSet = list.get(0);
        redisTemplate.opsForValue().set(RedisGroupPrefix.SYSRATES_SUFFIX + sysRatesSet.getRatesCompanyId().toString(), JsonMapper.toJsonString(sysRatesSet));
        if (rows > 0) {
            return JSONResponseUtil.success("设置成功");
        }
        return JSONResponseUtil.failure("设置失败！", -1);
    }


    /**
     * 服务费获取
     *
     * @param type 类型
     * @return 服务费列表
     */
    @GetMapping("/get")
    public ResponseMessage list(Integer type) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like("rates_company_id", securityInfoGetter.getCompId());
        queryWrapper.eq("rates_type", type);
        List<SysRatesSet> list = sysRatesSetMapper.selectList(queryWrapper);
        return JSONResponseUtil.success(list);
    }

    /**
     * 货损率设置
     *
     * @param sysRatesSet 货损率信息
     * @return 设置结果
     */
    @PostMapping(value = "/updateDamageAmount")
    @ResponseBody
    public ResponseMessage updateDamageAmount(@RequestBody SysRatesSet sysRatesSet) {
        if (CheckEmptyUtil.isEmpty(sysRatesSet)) {
            return JSONResponseUtil.failure("传入参数为空！", -1);
        }
        if (CheckEmptyUtil.isEmpty(sysRatesSet.getRatesId())) {
            return JSONResponseUtil.failure("id不可以为空！", -1);
        }
        if (CheckEmptyUtil.isEmpty(sysRatesSet.getDamageRate())) {
            return JSONResponseUtil.failure("货损率不可以为空！", -1);
        }
        LambdaUpdateWrapper<SysRatesSet> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(SysRatesSet::getRatesId, sysRatesSet.getRatesId());
        SysRatesSet param = new SysRatesSet();
        param.setDamageRate(sysRatesSet.getDamageRate());
        param.setUpdateTime(new Date());
        sysRatesSetMapper.update(param, lambdaUpdateWrapper);
        SysRatesSet dto = sysRatesSetMapper.selectById(sysRatesSet.getRatesId());
        redisTemplate.opsForValue().set(RedisGroupPrefix.SYSRATES_SUFFIX + dto.getRatesCompanyId().toString(), JsonMapper.toJsonString(dto));
        return JSONResponseUtil.success("设置成功");
    }


    /**
     * 新增运费计价模式
     *
     * @param sysRatesSet 运费计价模式信息
     * @return 新增结果
     */
    @PostMapping(value = "/insertFreighteMode")
    @ResponseBody
    public ResponseMessage insertFreighteMode(@RequestBody SysRatesSet sysRatesSet) {
        if (CheckEmptyUtil.isEmpty(sysRatesSet)) {
            return JSONResponseUtil.failure("传入参数为空！", -1);
        }
        if (CheckEmptyUtil.isEmpty(sysRatesSet.getRatesCompanyId())) {
            return JSONResponseUtil.failure("传入托运人公司ID为空！", -1);
        }
        try {
            int i = sysRatesSetService.insertFreighteMode(sysRatesSet);
            if (i > 0) {
                return JSONResponseUtil.success("设置成功");
            } else {
                return JSONResponseUtil.success("设置失败");
            }
        } catch (Exception ex) {
            return JSONResponseUtil.success(ex.getMessage());
        }
    }


    /**
     * 编辑运费计价模式
     *
     * @param sysRatesSet 运费计价模式信息
     * @return 编辑结果
     */
    @PostMapping(value = "/editFreighteMode")
    @ResponseBody
    public ResponseMessage editFreighteMode(@RequestBody SysRatesSet sysRatesSet) {
        if (CheckEmptyUtil.isEmpty(sysRatesSet)) {
            return JSONResponseUtil.failure("传入参数为空！", -1);
        }
        if (CheckEmptyUtil.isEmpty(sysRatesSet.getRatesId())) {
            return JSONResponseUtil.failure("传入ID为空！", -1);
        }
        try {
            int i = sysRatesSetService.editFreighteMode(sysRatesSet);
            if (i > 0) {
                return JSONResponseUtil.success("设置成功");
            } else {
                return JSONResponseUtil.success("设置失败");
            }
        } catch (Exception ex) {
            return JSONResponseUtil.success(ex.getMessage());
        }
    }

    /**
     * 查询运费计价模式
     *
     * @param ratesCompanyId 公司ID
     * @return 运费计价模式信息
     */
    @GetMapping(value = "/selectFreighteMode")
    @ResponseBody
    public ResponseMessage selectFreighteMode(String ratesCompanyId) {
        if (CheckEmptyUtil.isEmpty(ratesCompanyId)) {
            return JSONResponseUtil.failure("传入ID为空！", -1);
        }
        try {
            SysRatesSet i = sysRatesSetService.selectFreighteMode(ratesCompanyId);
            return JSONResponseUtil.success(i);
        } catch (Exception ex) {
            return JSONResponseUtil.success(ex.getMessage());
        }
    }

}
