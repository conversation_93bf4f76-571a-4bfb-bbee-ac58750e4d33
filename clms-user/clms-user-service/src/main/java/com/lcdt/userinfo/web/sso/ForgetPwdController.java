package com.lcdt.userinfo.web.sso;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.security.exception.GerenicRunException;
import com.lcdt.common.component.RedisCache;
import com.lcdt.common.constant.RedisGroupPrefix;
import com.lcdt.notify.rpcservice.ValidCodeService;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * 托运人/承运人忘记密码接口
 */
@RestController
@RequestMapping("/auth/forgetpwd")
public class ForgetPwdController {


    @Autowired
    private ValidCodeService validCodeService;

    @Autowired
    UserService userService;

    @Autowired
    private RedisCache redisCache;

    private String validcodeTag = "forgetpwd";


    /**
     * @param phoneNum '重置密码手机号'
     *                 重置密码
     *                 1、校验手机号码是否已经注册
     *                 2、发送手机验证码
     *                 3、注册有效期：15分钟；忘记密码有效期：5分钟；注册有效期：15分钟；
     */
    /**
     * 发送验证码
     *
     * @param request HTTP请求
     * @param phoneNum 手机号码
     * @param type 类型
     * @return 发送结果
     */
    @PostMapping("/sendforgetpwdcode")
    @ResponseBody
    public JSONObject forgetpwdSendValidCode(HttpServletRequest request, String phoneNum, Integer type) {
        String msg = "";
        int flag = 0;//是否可以发送验证码
        int tag = -1;
        if (StringUtils.isEmpty(phoneNum)) {
            msg = "手机号码不能为空!";
        } else {
            User user = userService.isPhoneRegister(phoneNum);
            if (user == null) {
                msg = "此手机号码暂未注册，请先注册！";
            } else {
                if (type == null || type == 1) {
                    if (user.getUserType().intValue() == 2 || user.getUserType().intValue() == 3) {
                        //手机号类型为承运人，type为空是托运人密码找回
                        msg = "该手机号密码找回身份与平台不符！";
                    } else {
                        flag = 1;
                    }
                } else {
                    if (user.getUserType().intValue() == 1 || user.getUserType().intValue() == 3) {
                        //手机号类型为托运人，type==2为承运人密码找回
                        msg = "该手机号密码找回身份与平台不符！";
                    } else {
                        flag = 1;
                    }
                }
                if (flag == 1) {
                    validCodeService.sendValidCode(validcodeTag, 60 * 5, phoneNum);
                    redisCache.setCacheObject(RedisGroupPrefix.FIND_PASSWORD_RETRY + phoneNum, 1, 60 * 5, TimeUnit.SECONDS);
                    tag = 0;
                    msg = "发送成功！";
                }
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", tag);
        jsonObject.put("message", msg);
        return jsonObject;
    }

    /**
     * @param phoneNum  '重置密码手机号'
     * @param validcode '验证码'
     * @param pwd       '新密码'
     *                  重置密码
     *                  1、校验手机号码是否已经注册
     *                  2、校验验证码是否有效（避免提交错误手机号码）
     *                  3、重置密码
     */
    /**
     * 重置密码
     *
     * @param request HTTP请求
     * @param phoneNum 手机号码
     * @param validcode 验证码
     * @param pwd 新密码
     * @return 重置结果
     */
    @PostMapping("/resetpwd")
    @ResponseBody
    public JSONObject forgetpwdSendValidCode(HttpServletRequest request, String phoneNum, String validcode, String pwd) {
        String key = RedisGroupPrefix.FIND_PASSWORD_RETRY + phoneNum;
        if (!redisCache.hasKey(key)) {
            throw new GerenicRunException("请点击获取验证码");
        } else {
            Integer retryTimes = redisCache.getCacheObject(key);
            if (retryTimes > 3) {
                throw new GerenicRunException("请重新获取验证码");
            } else {
                redisCache.increment(key, 1);
            }
        }
        JSONObject jsonObject = new JSONObject();
        boolean phoneBeenRegister = userService.isPhoneBeenRegister(phoneNum);
        if (!phoneBeenRegister) {
            jsonObject.put("code", -1);
            jsonObject.put("message", "此手机号码暂未注册，请先注册！");
            return jsonObject;
        }
        boolean codeCorrect = validCodeService.isCodeCorrect(validcode, validcodeTag, phoneNum);
        if (codeCorrect) {
            userService.resetPwd(phoneNum, pwd);
            jsonObject.put("code", 0);
            jsonObject.put("message", "重置密码成功！");
        } else {
            jsonObject.put("code", -1);
            jsonObject.put("message", "验证码错误或已过期，请重新获取！");
        }
        return jsonObject;
    }

}
