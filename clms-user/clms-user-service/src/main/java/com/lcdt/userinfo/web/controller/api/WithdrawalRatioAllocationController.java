package com.lcdt.userinfo.web.controller.api;

import com.lcdt.userinfo.model.WithdrawalRatioAllocation;
import com.lcdt.userinfo.service.WithdrawalRatioAllocationService;
import com.lcdt.userinfo.utils.JSONResponseUtil;
import com.lcdt.userinfo.utils.ResponseMessage;
import com.lcdt.userinfo.web.dto.PageResultDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 提现周期比例接口
 */
@RestController
@RequestMapping("/withdrawalRatioAllocation")
public class WithdrawalRatioAllocationController {


    @Autowired
    private WithdrawalRatioAllocationService withdrawalRatioAllocationService;


    /**
     * 查询提现周期比例
     *
     * @param withdrawalRatioAllocation 查询参数
     * @return 提现周期比例列表
     */
    @GetMapping("/list")
    public PageResultDto withdrawalRatioAllocationList(WithdrawalRatioAllocation withdrawalRatioAllocation) {
        List<WithdrawalRatioAllocation> withdrawalRatioAllocationList = withdrawalRatioAllocationService.queryList(withdrawalRatioAllocation);
        PageResultDto<WithdrawalRatioAllocation> balanceRecordPageResultDto = new PageResultDto<>(withdrawalRatioAllocationList);
        return balanceRecordPageResultDto;
    }

    /**
     * 新增提现周期比例
     *
     * @param withdrawalRatioAllocation 提现周期比例信息
     * @return 新增结果
     */
    @PostMapping("/insertWithdrawalRatioAllocation")
    public ResponseMessage insertWithdrawalRatioAllocation(WithdrawalRatioAllocation withdrawalRatioAllocation) {
        Integer i = withdrawalRatioAllocationService.insertWithdrawalRatioAllocation(withdrawalRatioAllocation);
        return JSONResponseUtil.success(i);
    }

    /**
     * 编辑提现周期比例
     *
     * @param withdrawalRatioAllocation 提现周期比例信息
     * @return 编辑结果
     */
    @PostMapping("/editWithdrawalRatioAllocation")
    public ResponseMessage editWithdrawalRatioAllocation(WithdrawalRatioAllocation withdrawalRatioAllocation) {
        Integer i = withdrawalRatioAllocationService.editWithdrawalRatioAllocation(withdrawalRatioAllocation);
        return JSONResponseUtil.success(i);
    }

}
