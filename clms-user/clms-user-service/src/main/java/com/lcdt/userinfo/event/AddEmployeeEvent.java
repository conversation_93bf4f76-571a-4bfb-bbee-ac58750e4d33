package com.lcdt.userinfo.event;

import org.springframework.context.ApplicationEvent;

public class AddEmployeeEvent extends ApplicationEvent{

    private boolean isNewUser;
    private String initPwd;//初始化密码

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public AddEmployeeEvent(Object source) {
        super(source);
    }

    public boolean isNewUser() {
        return isNewUser;
    }

    public void setNewUser(boolean newUser) {
        isNewUser = newUser;
    }

    public String getInitPwd() {
        return initPwd;
    }

    public void setInitPwd(String initPwd) {
        this.initPwd = initPwd;
    }
}
