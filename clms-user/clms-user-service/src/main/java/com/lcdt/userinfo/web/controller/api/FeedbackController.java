package com.lcdt.userinfo.web.controller.api;


import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.utils.StringUtils;
import com.lcdt.userinfo.dto.FeedbackDto;
import com.lcdt.userinfo.dto.FeedbackParamsDto;
import com.lcdt.userinfo.model.Feedback;
import com.lcdt.userinfo.service.FeedbackService;
import com.lcdt.userinfo.utils.JSONResponseUtil;
import com.lcdt.userinfo.utils.ResponseMessage;
import com.lcdt.userinfo.web.dto.PageResultDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 托运人投诉建议接口
 */
@RestController
@RequestMapping("/shipper/feedback")
public class FeedbackController {

    @Autowired
    SecurityInfoGetter securityInfoGetter;
    @Autowired
    FeedbackService feedbackService;

    /**
     * 托运人-投诉建议
     *
     * @param feedbackDto 反馈信息
     * @return 反馈结果
     */
    @PostMapping(value = "/save")
    public ResponseMessage saveFeedback(@RequestBody FeedbackDto feedbackDto) {
        Feedback feedback = new Feedback();
        feedback.setFbCompanyId(securityInfoGetter.getCompId());
        feedback.setFbUserId(securityInfoGetter.getUserInfo().getUserId());
        feedback.setFbUserName(StringUtils.isEmpty(securityInfoGetter.getUserInfo().getRealName())?(securityInfoGetter.getUserInfo().getPhone()):(securityInfoGetter.getUserInfo().getRealName()));
        feedback.setFbUserPhone(securityInfoGetter.getUserInfo().getPhone());
        feedback.setFbType(feedbackDto.getFbType());
        feedback.setFbContent(feedbackDto.getFbContent());
        feedbackService.save(feedback);
        return  JSONResponseUtil.success(null);
    }


    /**
     * 投诉列表
     *
     * @param feedbackParamsDto 查询参数
     * @return 投诉列表
     */
    @RequestMapping(value = "/list",method = RequestMethod.GET)
    public PageResultDto list(FeedbackParamsDto feedbackParamsDto)
    {
        feedbackParamsDto.setFbCompanyId(securityInfoGetter.getCompId());
        feedbackParamsDto.setType(1);
        List<Feedback> feedbackList = feedbackService.hzFeedbackList(feedbackParamsDto);
        PageResultDto<Feedback> feedbackPageResultDto = new PageResultDto<>(feedbackList);
        return feedbackPageResultDto;
    }


}
