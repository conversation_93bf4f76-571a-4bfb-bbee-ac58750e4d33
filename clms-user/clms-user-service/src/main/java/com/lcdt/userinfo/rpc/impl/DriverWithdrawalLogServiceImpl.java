package com.lcdt.userinfo.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.userinfo.dao.DriverWithdrawalLogDetailsMapper;
import com.lcdt.userinfo.dao.DriverWithdrawalLogMapper;
import com.lcdt.userinfo.dto.DriverWithdrawalLogDto;
import com.lcdt.userinfo.model.DriverWithdrawalLog;
import com.lcdt.userinfo.model.DriverWithdrawalLogDetail;
import com.lcdt.userinfo.rpc.DriverWithdrawalLogService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class DriverWithdrawalLogServiceImpl implements DriverWithdrawalLogService {
    @Autowired
    DriverWithdrawalLogMapper driverWithdrawalLogMapper;

    @Autowired
    private DriverWithdrawalLogDetailsMapper driverWithdrawalLogDetailsMapper;
    @Override
    public int addDriverWithdrawalLog(DriverWithdrawalLog driverWithdrawalLog) {
        return driverWithdrawalLogMapper.insert(driverWithdrawalLog);
    }

    @Override
    public int updateWithdrawalStatus(String outTradeNo,String orderNo, int status){
       return  driverWithdrawalLogMapper.updateWithdrawalStatus(outTradeNo,orderNo,status);
    }

    @Override
    public PageInfo<DriverWithdrawalLog> selectList(DriverWithdrawalLogDto driverWithdrawalDto) {
        List<DriverWithdrawalLog> withdrawalLog = driverWithdrawalLogMapper.selectLogList(driverWithdrawalDto);
        return new PageInfo<>(withdrawalLog);
    }

    @Override
    public int updateWithdrawalLog(DriverWithdrawalLog driverWithdrawalLog) {
       return driverWithdrawalLogMapper.updateById(driverWithdrawalLog);
    }

    @Override
    public List<DriverWithdrawalLog> selectArray(DriverWithdrawalLog driverWithdrawalLog) {
        return driverWithdrawalLogMapper.selectArray(driverWithdrawalLog);
    }

    @Override
    public DriverWithdrawalLog selectTotal(DriverWithdrawalLogDto driverWithdrawalDto) {
        return driverWithdrawalLogMapper.selectTotal(driverWithdrawalDto);
    }

    @Override
    public DriverWithdrawalLog selectBySerialNo(String reqSeqNo) {
        return driverWithdrawalLogMapper.selectBySerialNo(reqSeqNo);
    }

    @Override
    public List<DriverWithdrawalLog> queryList(DriverWithdrawalLog withdrawalLog) {
        PageHelper.startPage(withdrawalLog.getPageNo(), withdrawalLog.getPageSize());
        List<DriverWithdrawalLog> list =  driverWithdrawalLogMapper.queryList(withdrawalLog);
        for (DriverWithdrawalLog driverWithdrawalLog : list) {
            LambdaQueryWrapper<DriverWithdrawalLogDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DriverWithdrawalLogDetail::getRecordId,driverWithdrawalLog.getRecordId());
            List<DriverWithdrawalLogDetail> driverWithdrawalLogDetails = driverWithdrawalLogDetailsMapper.selectList(lambdaQueryWrapper);
            driverWithdrawalLog.setDriverWithdrawalLogDetailList(driverWithdrawalLogDetails);
        }
        return list;
    }
}
