package com.lcdt.userinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.userinfo.dao.DriverLoginLogMapper;
import com.lcdt.userinfo.dto.DriverLoginLogListParams;
import com.lcdt.userinfo.model.DriverLoginLog;
import com.lcdt.userinfo.service.DriverLoginLogService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-14
 */
@Service
public class DriverLoginLogServiceImpl implements DriverLoginLogService {

    @Autowired
    private DriverLoginLogMapper driverLoginLogMapper;

    @Override
    public IPage<DriverLoginLog> queryDriverLoginLogListByCondition(DriverLoginLogListParams params) {
        Page<DriverLoginLog> page = new Page<>(params.getPageNo(), params.getPageSize());
        LambdaQueryWrapper<DriverLoginLog> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.like(StringUtils.isNotBlank(params.getDriverName()), DriverLoginLog::getDriverName, params.getDriverName())
                .like(StringUtils.isNotBlank(params.getAppVersionName()), DriverLoginLog::getAppVersionName, params.getAppVersionName())
                .eq(Objects.nonNull(params.getAppVersionCode()), DriverLoginLog::getAppVersionCode, params.getAppVersionCode())
                .orderByDesc(DriverLoginLog::getLogId).ge(Objects.nonNull(params.getBeginTime()), DriverLoginLog::getLoginTime, params.getBeginTime())
                .le(Objects.nonNull(params.getEndTime()), DriverLoginLog::getLoginTime, params.getEndTime());
        return driverLoginLogMapper.selectPage(page, lambdaQueryWrapper);
    }

    @Override
    public DriverLoginLog getLogByPhone(String driverPhone) {
        return driverLoginLogMapper.selectLastByDriverPhone(driverPhone);
    }

    @Override
    public int save(DriverLoginLog driverLoginLog) {
        return driverLoginLogMapper.insert(driverLoginLog);
    }

}
