package com.lcdt.userinfo.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.userinfo.dao.ExportInfoMapper;
import com.lcdt.userinfo.dto.ExportInfoDto;
import com.lcdt.userinfo.model.ExportInfo;
import com.lcdt.userinfo.rpc.ExportInfoRpcService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by ybq on 2020/8/13 15:57
 */
@Service
@Transactional
public class ExportInfoRpcServiceImpl implements ExportInfoRpcService {


    @Autowired
    private ExportInfoMapper exportInfoMapper;

    @Override
    public Long addExportInfo(ExportInfo exportInfo) {
         exportInfoMapper.insert(exportInfo);
         return exportInfo.getEid();
    }

    @Override
    public int updateExportInfo(ExportInfo exportInfo) {
        return exportInfoMapper.updateById(exportInfo);
    }

    @Override
    public PageInfo<ExportInfo> exportInfoList(ExportInfoDto exportInfoDto) {
        PageHelper.startPage(exportInfoDto.getPageNo(),exportInfoDto.getPageSize());
        QueryWrapper qw = new QueryWrapper<>();
        qw.eq("company_id",exportInfoDto.getCompanyId());
        qw.orderByDesc("eid");
        return new PageInfo<>(exportInfoMapper.selectList(qw));
    }


}



