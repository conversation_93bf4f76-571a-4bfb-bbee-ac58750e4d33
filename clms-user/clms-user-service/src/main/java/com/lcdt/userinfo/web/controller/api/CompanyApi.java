package com.lcdt.userinfo.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.common.config.SettingProperties;
import com.lcdt.common.prop.AbcProperties;
import com.lcdt.pay.abc.service.AbcApiService;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.traffic.vo.ConstantVO;
import com.lcdt.userinfo.dto.CompanyDto;
import com.lcdt.userinfo.dto.ExportInfoDto;
import com.lcdt.userinfo.dto.ModifyCompanyAuthDto;
import com.lcdt.userinfo.dto.ModifyCompanyInfoDto;
import com.lcdt.userinfo.model.*;
import com.lcdt.userinfo.rpc.ExportInfoRpcService;
import com.lcdt.userinfo.rpc.SysRatesSetService;
import com.lcdt.userinfo.service.CompanyService;
import com.lcdt.userinfo.service.UserService;
import com.lcdt.userinfo.utils.BeanProperties;
import com.lcdt.userinfo.utils.JSONResponseUtil;
import com.lcdt.userinfo.utils.ResponseMessage;
import com.lcdt.userinfo.web.dto.ModifyContactDto;
import com.lcdt.userinfo.web.exception.CompanyNameExistException;
import com.lcdt.util.CheckEmptyUtil;
import com.lcdt.util.ResponseJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * Created by ss on 2017/11/3.
 */
/**
 * 托运人/承运人认证api
 */
@RestController
@RequestMapping("/api/company")
public class CompanyApi {

    @Autowired
    private CompanyService companyService;

    @Autowired
    private UserService userService;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private ExportInfoRpcService exportInfoRpcService;

    @Autowired
    private SysRatesSetService sysRatesSetService;


    @Autowired
    private AbcApiService abcApiService;

    @Autowired
    private AbcProperties abcProperties;

    @Autowired
    private SettingProperties settingProperties;


    /**
     * 提交公司认证图片信息
     *
     * @param dto 认证信息
     * @return 认证结果
     * @throws IOException IO异常
     */
    @PostMapping(value = "/updateAuth")
    public CompanyCertificate updateAuthInfo(@Validated ModifyCompanyAuthDto dto) throws IOException {
        Long companyId = dto.getCompId();
        CompanyCertificate companyCertificate = companyService.queryCertByCompanyId(companyId);
        if (companyCertificate == null) {
            companyCertificate = new CompanyCertificate();
            companyCertificate.setCompId(companyId);
        }
        BeanProperties.copyProperties(dto, companyCertificate, null, null);
        companyService.updateCompanyCert(companyCertificate, (short) 1);
        return companyCertificate;
    }

    /**
     * 创建认证
     *
     * @param companyDto 公司信息
     * @param dto 认证信息
     * @return 创建结果
     */
    @PostMapping("/createAuthen")
    public ResponseMessage createAuthen(CompanyDto companyDto, ModifyCompanyAuthDto dto) {
        User user = securityInfoGetter.getUserInfo();
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        Company company = null;
        if (securityInfoGetter.getCompId() == null) {
            companyDto.setCreateId(user.getUserId());
            companyDto.setCreateName(user.getRealName());
            companyDto.setUserId(user.getUserId());
            companyDto.setCompanyName(companyDto.getFullName());
            if (!(3 == companyDto.getCompanyType())) {
                companyDto.setCompanyType(user.getUserType()); //用户类型
            }
            companyDto.setEnable(true); //默认启用
            companyDto.setEnterStatus(0);
            company = companyService.createCompany4(companyDto);
        } else {
            if (companyService.checkCompanyExists(securityInfoGetter.getCompId(), companyDto.getFullName())) {
                throw new RuntimeException("企业名称已存在！");
            }
            company = companyService.selectById(securityInfoGetter.getCompId());
            Short authentication = company.getAuthentication();
            BeanProperties.copyProperties(companyDto, company, null, null);
            if (company.getAuthentication() == 3) { //认证失败-认证中
                company.setAuthentication((short) 1);
            } else {
                company.setAuthentication(authentication); //认证回填
            }
            companyService.updateCompany(company);
        }
        if (!ObjectUtils.isEmpty(company)) {
            CompanyCertificate companyCertificate = companyService.queryCertByCompanyId(company.getCompId());
            if (companyCertificate == null) {
                companyCertificate = new CompanyCertificate();
                companyCertificate.setCompId(company.getCompId());
            }
            BeanProperties.copyProperties(dto, companyCertificate, null, null);
            companyService.updateCompanyCert(companyCertificate, (short) 1);
            UserCompRel userCompRel = companyService.queryByUserIdCompanyId(user.getUserId(), company.getCompId());
            if (!ObjectUtils.isEmpty(userCompRel)) {
                securityInfoGetter.setCompanyRef(userCompRel);
            }
        }
        return JSONResponseUtil.success(company);
    }

    /**
     * 企业创建
     *
     * @param companyDto 公司信息
     * @return 创建结果
     */
    @PostMapping("/createCompany")
    public ResponseMessage createCompany(CompanyDto companyDto) {
        User user = securityInfoGetter.getUserInfo();
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        companyDto.setCreateId(user.getUserId());
        companyDto.setCreateName(user.getPhone());//创建者改为创建者手机号
        companyDto.setUserId(user.getUserId());
        companyDto.setCompanyName(companyDto.getFullName());
        companyDto.setCompanyType(user.getUserType()); //用户类型
        companyDto.setEnable(true); //默认启用
        companyDto.setEnterStatus(0);
        Company company = companyService.createCompany4(companyDto);
        if (!ObjectUtils.isEmpty(company)) {
            UserCompRel userCompRel = companyService.queryByUserIdCompanyId(user.getUserId(), company.getCompId());
            if (!ObjectUtils.isEmpty(userCompRel)) {
                securityInfoGetter.setCompanyRef(userCompRel);
            }
        }
        return JSONResponseUtil.success(company);
    }

    /**
     * 获取公司认证图片信息
     *
     * @return 认证信息
     */
    @GetMapping(value = "/getauthinfo")
    public CompanyCertificate getAuthInfo() {
        Long companyId = securityInfoGetter.getCompId();
        return companyService.getCompanyCert(companyId);
    }


    /**
     * 修改开票信息
     *
     * @param dto 开票信息
     * @return 修改结果
     */
    @RequestMapping(value = "/updateInvoice", method = RequestMethod.POST)
    public Company updateInvoiceInfo(@Validated CompanyDto dto) {
        Company company = updateCompanyWithDto(dto);
        return company;
    }


    /**
     * 编辑公司联系人信息
     *
     * @return
     */
    @PostMapping(value = "/update_contact")
    /**
     * 修改公司联系人信息
     *
     * @param modifyContactDto 联系人信息
     * @return 修改结果
     */
    public Company updateCompanyContact(@Validated ModifyContactDto modifyContactDto) {
        Long companyId = securityInfoGetter.getCompId();
        Company company = companyService.selectById(companyId);

        company.setLinkMan(modifyContactDto.getContactName());
        company.setLinkTel(modifyContactDto.getContacTel());

        if (!StringUtils.isEmpty(modifyContactDto.getContactRemark())) {
            company.setLinkRemark(modifyContactDto.getContactRemark());
        }

        if (!StringUtils.isEmpty(modifyContactDto.getContactEmail())) {
            company.setLinkEmail(modifyContactDto.getContactEmail());
        }

        if (!StringUtils.isEmpty(modifyContactDto.getContactDuty())) {
            company.setLinkDuty(modifyContactDto.getContactDuty());
        }
        companyService.updateCompany(company);
        return company;
    }


    /**
     * 修改企业信息
     *
     * @param modifyCompanyInfoDto 企业信息
     * @return 修改结果
     * @throws CompanyNameExistException 企业名称已存在异常
     */
    @PostMapping(value = "/update_company")
    public Company updateCompanyInfo(@Validated ModifyCompanyInfoDto modifyCompanyInfoDto) throws CompanyNameExistException {
        Long companyId = securityInfoGetter.getCompId();
        Company company = companyService.selectById(companyId);
        company.setShortName(modifyCompanyInfoDto.getShortName());

        if (company.getAuthentication() == 3) { //认证失败-认证中
            company.setAuthentication((short) 1);
        }


        if (!StringUtils.isEmpty(modifyCompanyInfoDto.getFullName()) && !modifyCompanyInfoDto.getFullName().equals(company.getFullName())) {
            CompanyDto companyDto = new CompanyDto();
            companyDto.setCompanyName(modifyCompanyInfoDto.getFullName());
            Company company1 = companyService.findCompany(companyDto);
            if (company1 != null) {
                throw new CompanyNameExistException("企业名称已经存在!");
            }
        }
        company.setFullName(modifyCompanyInfoDto.getFullName());
        company.setShortName(modifyCompanyInfoDto.getShortName());
        company.setPlatformName(modifyCompanyInfoDto.getPlatformName());
        company.setIndustry(modifyCompanyInfoDto.getIndustry());
        company.setProvince(modifyCompanyInfoDto.getProvince());
        company.setCity(modifyCompanyInfoDto.getCity());
        company.setCounty(modifyCompanyInfoDto.getCounty());
        company.setDetailAddress(modifyCompanyInfoDto.getDetailAddress());

        if (!StringUtils.isEmpty(modifyCompanyInfoDto.getCompIntro())) {
            company.setCompIntro(modifyCompanyInfoDto.getCompIntro());
        } else {
            company.setCompIntro(" ");
        }

        if (!StringUtils.isEmpty(modifyCompanyInfoDto.getLinkMan())) {
            company.setLinkMan(modifyCompanyInfoDto.getLinkMan());
        } else {
            company.setLinkMan(" ");
        }

        if (!StringUtils.isEmpty(modifyCompanyInfoDto.getLinkTel())) {
            company.setLinkTel(modifyCompanyInfoDto.getLinkTel());
        } else {
            company.setLinkTel(" ");
        }
        companyService.updateCompany(company);
        return company;
    }

    /**
     * 修改企业报价隐藏属性
     *
     * @param offerHide 隐藏属性
     * @return 修改结果
     */
    @PostMapping(value = "/updateCompanyOfferHide")
    public Company updateCompanyOfferHide(String offerHide) {
        Long companyId = securityInfoGetter.getCompId();
        Company company = companyService.selectById(companyId);
        company.setOfferHide(offerHide);
        companyService.updateCompany(company);
        return company;
    }


    @GetMapping(value = "/companyinfo")
    /**
     * 获取企业信息
     *
     * @return 企业信息
     */
    public JSONObject companyInfo() {
        Long companyId = securityInfoGetter.getCompId();
        Company company = companyService.selectById(companyId);
        SysRatesSet sysRatesSet = sysRatesSetService.queryRatesByCompanyId(companyId);
        if (CheckEmptyUtil.isNotEmpty(sysRatesSet)) {
            company.setRatesFrist(sysRatesSet.getRatesFrist());
            company.setRatesSecond(sysRatesSet.getRatesSecond());
            company.setDamageRate(sysRatesSet.getDamageRate());
            company.setDirectPlanFlag(sysRatesSet.getDirectPlanFlag());
            company.setDirectPlanVehiclePrice(sysRatesSet.getDirectPlanVehiclePrice());
            company.setDriectPlanAmountType(sysRatesSet.getDriectPlanAmountType());
            company.setFixedPlanFlag(sysRatesSet.getFixedPlanFlag());
            company.setFixedPlanVehiclePrice(sysRatesSet.getFixedPlanVehiclePrice());
            company.setFixedPlanAmountType(sysRatesSet.getFixedPlanAmountType());
        }
        JSONObject jsonObject = new JSONObject();
        if (!ObjectUtils.isEmpty(company)) {
            company.setCompanyCertificate(companyService.getCompanyCert(companyId));
            //返回监管户账号+账簿号充值使用
            //安徽山东分开
            if(ConstantVO.sdAffiliatedPlatform.equals(company.getAffiliatedPlatform())){
                company.setSupervisionAccount(abcProperties.getAccountNoPre() + abcProperties.getAccountNo());
            }else if(ConstantVO.ahAffiliatedPlatform.equals(company.getAffiliatedPlatform())){
                company.setSupervisionAccount(abcProperties.getAccountNoPre() + abcProperties.getAhAccountNo());
            }
            // todo xingyuan
            String qrCode = settingProperties.getQrCode();
            if (CheckEmptyUtil.isNotEmpty(qrCode)) {
                company.setTenantQrCode(qrCode);
            }
            // 设置平台名称
            jsonObject.put("company", company);
        } else {
            jsonObject.put("company", null);
        }
        return ResponseJsonUtils.successResponseJson(jsonObject);
    }

    @PostMapping(value = "/pf/push")
    /**
     * 获取托运人余额
     *
     * @return 余额信息
     */
    public JSONObject platformInfoPush() {
        Long companyId = securityInfoGetter.getCompId();
        Company company = companyService.selectById(companyId);
        CompanyCertificate companyCert = companyService.getCompanyCert(companyId);
        throw new RuntimeException("接口失效，请同步删除该功能");
    }

    @PostMapping(value = "/shipper/createQrCode")
    /**
     * 创建二维码
     *
     * @return 二维码信息
     */
    public JSONObject createQrCode() {
        Long companyId = securityInfoGetter.getCompId();
        Company company = companyService.selectById(companyId);
        return companyService.createCompanyInviteDriverPic(company);
    }


    @PostMapping(value = "/shipper/getAccount")
    /**
     * 获取托运人余额
     *
     * @return 余额信息
     */
    public JSONObject getAccountShipper() {
        Long companyId = securityInfoGetter.getCompId();
        Company company = companyService.selectById(companyId);
        if (CheckEmptyUtil.isEmpty(company.getAbNo())) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("该企业没有开通农行账簿");
        }
        return companyService.getAccount(company);
    }

    @PostMapping(value = "/carrier/getAccount")
    /**
     * 获取运营端余额
     *
     * @param affiliatedPlatform 所属平台
     * @return 余额信息
     */
    public JSONObject getAccountAarrier(String affiliatedPlatform) {
        Company company = new Company();
        if(ConstantVO.sdAffiliatedPlatform.equals(affiliatedPlatform)){
            Long companyId = securityInfoGetter.getCompId();
            company = companyService.selectById(companyId);
            if (CheckEmptyUtil.isEmpty(company.getAbNo())) {
                return ResponseJsonUtils.failedResponseJsonWithoutData("该企业没有开通农行账簿");
            }
        }
        return companyService.getCarrierAccount(affiliatedPlatform,company);
    }

    private Company updateCompanyWithDto(CompanyDto dto) {
        Long companyId = securityInfoGetter.getCompId();
        dto.setCompId(companyId);
        Company company = companyService.updateHaveNullCompany(dto);
        return company;
    }

    @PostMapping(value = "/edit/quota")
    /**
     * 设置货主额度
     *
     * @param companyQuota 额度信息
     * @return 设置结果
     */
    public JSONObject editQuota(@RequestBody CompanyQuota companyQuota) {
        Long companyId = securityInfoGetter.getCompId();
        try {
            companyService.editQuota(companyQuota, companyId);
            return ResponseJsonUtils.successResponseJson("设置成功");
        } catch (Exception ex) {
            return ResponseJsonUtils.failedResponseJson(ex.getMessage());
        }
    }


    /**
     * 导出消息
     *
     * @param exportInfoDto 导出参数
     * @return 导出结果
     */
    @GetMapping("/exportMsgList")
    public JSONObject exportMsgList(ExportInfoDto exportInfoDto) {
        exportInfoDto.setCompanyId(securityInfoGetter.getCompId());
        return ResponseJsonUtils.successResponseJson(exportInfoRpcService.exportInfoList(exportInfoDto));
    }


}
