<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.CompanyThirdMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.CompanyThird">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="comp_id" jdbcType="BIGINT" property="compId" />
    <result column="tjsw_dduuid" jdbcType="VARCHAR" property="tjswDduuid" />
    <result column="tjsw_push_status" jdbcType="INTEGER" property="tjswPushStatus" />
    <result column="tjsw_push_fail_msg" jdbcType="VARCHAR" property="tjswPushFailMsg" />
    <result column="khy_push_status" jdbcType="INTEGER" property="khyPushStatus" />
    <result column="khy_push_fail_msg" jdbcType="VARCHAR" property="khyPushFailMsg" />
  </resultMap>
  <sql id="Base_Column_List">
    id, comp_id, tjsw_dduuid, tjsw_push_status, tjsw_push_fail_msg, khy_push_status, khy_push_fail_msg
  </sql>

</mapper>