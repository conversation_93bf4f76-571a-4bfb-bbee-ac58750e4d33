package com.lcdt.userinfo.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.userinfo.dao.ArticlesMapper;
import com.lcdt.userinfo.model.Articles;
import com.lcdt.userinfo.service.ArticlesService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【uc_articles(文章表)】的数据库操作Service实现
 * @createDate 2025-07-18 09:37:19
 */
@Service
public class ArticlesServiceImpl extends ServiceImpl<ArticlesMapper, Articles>
        implements ArticlesService {

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Override
    public IPage<Articles> queryArticlePage(Integer pageNum, Integer pageSize, String title, Integer type) {
        // 创建分页对象
        Page<Articles> page = new Page<>(pageNum, pageSize);
        // 构建查询条件
        LambdaQueryWrapper<Articles> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(title)) {
            queryWrapper.like(Articles::getTitle, title);
        }
        if (type != null) {
            queryWrapper.eq(Articles::getType, type);
        }
        // 按创建时间倒序
        queryWrapper.orderByDesc(Articles::getCreateTime);

        // 执行分页查询
        return baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    public Articles getArticleDetail(Long id) {
        if (id == null) {
            return null;
        }
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addArticle(Articles article) {
        if (article == null) {
            return false;
        }
        // 设置创建人信息
        Long currentUserId = securityInfoGetter.getUserInfo().getUserId();
        String currentUsername = securityInfoGetter.getUserInfo().getRealName();
        article.setCreateId(currentUserId);
        article.setCreateName(currentUsername);
        if (ObjectUtil.isNull(article.getPublishTime())) {
            article.setPublishTime(new Date());
        }
        return save(article);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateArticle(Articles article) {
        operateCheck(article.getId());
        // 设置更新时间
        article.setUpdateTime(new Date());
        // 更新时不清空创建人信息
        article.setCreateId(null);
        article.setCreateName(null);
        article.setCreateTime(null);

        return updateById(article);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteArticle(Long id) {
        operateCheck(id);
        return removeById(id);
    }

    private void operateCheck(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new IllegalArgumentException("文章id不能为空");
        }
        Articles byId = getById(id);
        if (!Objects.equals(securityInfoGetter.getUserInfo().getUserId(), byId.getCreateId())) {
            throw new IllegalArgumentException("无法删除其他用户的文章");
        }

    }
}




