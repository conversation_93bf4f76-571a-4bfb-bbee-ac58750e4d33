package com.lcdt.userinfo.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.userinfo.dto.CompGroupQueryDto;
import com.lcdt.userinfo.dto.GroupQueryDto;
import com.lcdt.userinfo.dto.UserGroupQueryDto;
import com.lcdt.userinfo.model.CompanyGroup;
import com.lcdt.userinfo.model.Group;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.model.UserGroup;
import com.lcdt.userinfo.rpc.GroupRpcService;
import com.lcdt.userinfo.web.dto.PageBaseDto;
import com.lcdt.util.CheckEmptyUtil;
import com.lcdt.util.ResponseJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by ybq on 2020/9/7 11:44
 */
/**
 * 承运端-项目组管理接口
 */
@RestController
@RequestMapping("/group")
public class GroupController {

    @Autowired
    private GroupRpcService groupRpcService;


    @Autowired
    private SecurityInfoGetter securityInfoGetter;


    /**
     * 获取项目组列表
     *
     * @param dto 查询参数
     * @return 项目组列表
     */
    @GetMapping(value = "/list")
    public JSONObject list(GroupQueryDto dto) {
        dto.setCompanyId(securityInfoGetter.getCompId()); //承运人企业ID
        PageInfo pg = groupRpcService.groupList(dto);
        if (CollectionUtils.isEmpty(pg.getList())) {
            pg.setTotal(0);
        }
        return ResponseJsonUtils.successResponseJson(new PageBaseDto(pg.getList(), pg.getTotal()));
    }


    /**
     * 保存项目组
     *
     * @param group 项目组信息
     * @return 保存结果
     */
    @PostMapping(value = "/create")
    public JSONObject create(Group group) {
        group.setCompanyId(securityInfoGetter.getCompId());
        int result = groupRpcService.addGroup(group);
        if (result > 0) {
            return ResponseJsonUtils.successResponseJson("操作成功！");
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("保存失败！");
    }

    /**
     * 修改项目组
     *
     * @param group 项目组信息
     * @return 修改结果
     */
    @PostMapping(value = "/modify")
    public JSONObject modifyGroup(Group group) {
        group.setCompanyId(securityInfoGetter.getCompId());
        int result = groupRpcService.modifyGroup(group);
        if (result > 0) {
            return ResponseJsonUtils.successResponseJson("操作成功！");
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("操作失败！");
    }


    /**
     * 启停项目组 0-停止/ 1-开启
     *
     * @param groupId 项目组ID
     * @param flag 启停标志
     * @return 操作结果
     */
    @PostMapping(value = "/startStop")
    public JSONObject startStop(Long groupId, int flag) {
        Group group = new Group();
        group.setCompanyId(securityInfoGetter.getCompId());
        group.setGroupId(groupId);
        group.setStatus(flag);
        int result = groupRpcService.startStop(group);
        if (result > 0) {
            return ResponseJsonUtils.successResponseJson("操作成功！");
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("操作失败！");
    }


    /**
     * 员工管理
     *
     * @param dto 查询参数
     * @return 员工列表
     */
    @GetMapping(value = "/user-list")
    public JSONObject userList(UserGroupQueryDto dto) {
        dto.setCompanyId(securityInfoGetter.getCompId()); //承运人企业ID
        PageInfo pg = groupRpcService.groupUserList(dto);
        return ResponseJsonUtils.successResponseJson(new PageBaseDto(pg.getList(), pg.getTotal()));
    }

    /**
     * 员工保存
     *
     * @param userGroup 用户组信息
     * @return 保存结果
     */
    @PostMapping(value = "/user-add")
    public JSONObject uesrAdd(UserGroup userGroup) {
        userGroup.setCompanyId(securityInfoGetter.getCompId()); //承运人企业ID
        int flag = groupRpcService.addUserGroup(userGroup);
        if (flag > 0) {
            return ResponseJsonUtils.successResponseJson("操作成功！");
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("操作失败！");
    }

    /**
     * 员工移除
     *
     * @param userGroup 用户组信息
     * @return 移除结果
     */
    @PostMapping(value = "/user-remove")
    public JSONObject uesrRemove(UserGroup userGroup) {
        userGroup.setCompanyId(securityInfoGetter.getCompId()); //承运人企业ID
        int flag = groupRpcService.removeUserGroup(userGroup);
        if (flag > 0) {
            return ResponseJsonUtils.successResponseJson("操作成功！");
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("操作失败！");
    }


    /**
     * 企业管理
     *
     * @param dto 查询参数
     * @return 企业列表
     */
    @GetMapping(value = "/cmp-list")
    public JSONObject userList(CompGroupQueryDto dto) {
        PageInfo pg = groupRpcService.groupCmpList(dto);
        return ResponseJsonUtils.successResponseJson(new PageBaseDto(pg.getList(), pg.getTotal()));
    }


    /**
     * 企业保存
     *
     * @param companyGroup 企业组信息
     * @return 保存结果
     */
    @PostMapping(value = "/company-add")
    public JSONObject companyAdd(CompanyGroup companyGroup) {
        companyGroup.setCompanyId(securityInfoGetter.getCompId()); //承运人企业ID
        int flag = groupRpcService.addCompanyGroup(companyGroup);
        if (flag > 0) {
            return ResponseJsonUtils.successResponseJson("操作成功！");
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("保存失败！");
    }


    /**
     * 企业移除
     *
     * @param companyGroup 企业组信息
     * @return 移除结果
     */
    @PostMapping(value = "/company-remove")
    public JSONObject companyRemove(CompanyGroup companyGroup) {
        companyGroup.setCompanyId(securityInfoGetter.getCompId()); //承运人企业ID
        int flag = groupRpcService.removeCompanyGroup(companyGroup);
        if (flag > 0) {
            return ResponseJsonUtils.successResponseJson("操作成功！");
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("保存失败！");
    }


    /**
     * 用户组列表
     *
     * @return 用户组列表
     */
    @GetMapping(value = "/user-group-list")
    public JSONObject userGroupList() {
        User user = securityInfoGetter.getUserInfo();
        if (user.getIsSub() == 1) { //如果是主账号
            GroupQueryDto dto = new GroupQueryDto();
            dto.setCompanyId(securityInfoGetter.getCompId());
            return ResponseJsonUtils.successResponseJson(groupRpcService.groupAllList(dto));
        }else {
//            子账号仅显示所在的业务组的，如果是多个业务组，默认显示全部。
//            子账号如果未设置业务组，同管理员的显示
            List<Group> groups = groupRpcService.groupList4UserId(user.getUserId(),
                    securityInfoGetter.getCompId());
            if(CheckEmptyUtil.isNotEmpty(groups)){
                 return ResponseJsonUtils.successResponseJson(groups);
            }else {
                GroupQueryDto dto = new GroupQueryDto();
                dto.setCompanyId(securityInfoGetter.getCompId());
                return ResponseJsonUtils.successResponseJson(groupRpcService.groupAllList(dto));
            }
        }
    }
}
