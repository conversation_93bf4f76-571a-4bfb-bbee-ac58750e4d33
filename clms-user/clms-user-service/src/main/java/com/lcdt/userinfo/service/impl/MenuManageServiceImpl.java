package com.lcdt.userinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lcdt.security.exception.CustomException;
import com.lcdt.userinfo.dao.MenuMapper;
import com.lcdt.userinfo.dto.MenuDto;
import com.lcdt.userinfo.model.Menu;
import com.lcdt.userinfo.service.MenuManageService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MenuManageServiceImpl implements MenuManageService {
    @Autowired
    private MenuMapper menuMapper;


    @Override
    public List<MenuDto> menuList(Long menuPid, Integer sysType) {
        return menuMapper.selectByPid(menuPid, sysType);
    }

    @Override
    public int delMenu(Long menuId) {
        // 先判断该菜单下是否存在子菜单，如果存在则无法删除
        Long count = menuMapper.selectCount(new QueryWrapper<Menu>().lambda()
                .eq(Menu::getMenuPid, menuId));
        if (count > 0) {
            throw new CustomException("该菜单下面存在子菜单，无法删除");
        }
        return menuMapper.deleteById(menuId);
    }

    @Override
    public List<Menu> permsList(Integer sysType) {
        return menuMapper.selectList(new QueryWrapper<Menu>().lambda()
                .select(Menu::getMenuName, Menu::getMenuCode)
                .eq(Menu::getSysType, sysType));
    }

    @Override
    public List<Menu> getMenuName(String perms) {
        return menuMapper.selectList(new QueryWrapper<Menu>().lambda()
                .select(Menu::getMenuName).likeRight(Menu::getPerms, perms));
    }
}
