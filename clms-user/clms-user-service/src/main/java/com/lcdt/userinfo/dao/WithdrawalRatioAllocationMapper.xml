<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.WithdrawalRatioAllocationMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.WithdrawalRatioAllocation">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="allocation_cycle_begin" jdbcType="VARCHAR" property="allocationCycleBegin" />
        <result column="allocation_cycle_end" jdbcType="VARCHAR" property="allocationCycleEnd" />
        <result column="allocation_proportion" jdbcType="VARCHAR" property="allocationProportion" />
        <result column="affiliated_platform" jdbcType="VARCHAR" property="affiliatedPlatform" />
        <result column="enabled" jdbcType="INTEGER" property="enabled" />
        <result column="create_id" jdbcType="VARCHAR" property="createId" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    </resultMap>
    <select id="queryList" resultType="com.lcdt.userinfo.model.WithdrawalRatioAllocation"
            parameterType="com.lcdt.userinfo.model.WithdrawalRatioAllocation">
        select *
        from uc_withdrawal_ratio_allocation
        where
        <if test="affiliatedPlatform!=null and affiliatedPlatform!=''">
            affiliated_platform = #{affiliatedPlatform,jdbcType=VARCHAR}
        </if>
        order by create_date desc
    </select>

</mapper>