package com.lcdt.userinfo.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lcdt.common.component.RedisCache;
import com.lcdt.common.constant.RedisGroupPrefix;
import com.lcdt.userinfo.dao.CompanyMapper;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.rpc.CarrierInfoRpcService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @date 2022-02-21
 */
@Service
public class CarrierInfoRpcServiceImpl implements CarrierInfoRpcService {

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private RedisCache redisCache;

    @Override
    public Long getCarrierId() {
        String key = RedisGroupPrefix.CARRIER_ID;
        Object cacheObject = redisCache.getCacheObject(key);
        if (ObjectUtils.isEmpty(cacheObject)) {

            Company company = companyMapper.selectOne(new LambdaQueryWrapper<Company>()
                    .eq(Company::getCompanyType, 2));
            redisCache.setCacheObject(key, company.getCompId());
            return company.getCompId();
        } else {
            return redisCache.getCacheObject(key);
        }
    }

    @Override
    public String getMerchantId() {
        String key = RedisGroupPrefix.CARRIER_MERCHANT_ID;
        Object cacheObject = redisCache.getCacheObject(key);
        if (ObjectUtils.isEmpty(cacheObject)) {
            Company company = companyMapper.selectOne(new LambdaQueryWrapper<Company>()
                    .eq(Company::getCompanyType, 2));
            redisCache.setCacheObject(key, company.getMerchantId());
            return company.getMerchantId();
        } else {
            return redisCache.getCacheObject(key);
        }
    }
}
