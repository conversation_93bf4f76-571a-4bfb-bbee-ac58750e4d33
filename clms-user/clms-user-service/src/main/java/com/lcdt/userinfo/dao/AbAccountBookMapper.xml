<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.AbAccountBookMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.AbAccountBook">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="other_id" jdbcType="BIGINT" property="otherId"/>
        <result column="ab_no" jdbcType="VARCHAR" property="abNo"/>
    </resultMap>

    <select id="selectMaxAbNo" resultType="java.lang.String">
        select max(ab_no) from uc_ab_account_book;
    </select>
</mapper>