package com.lcdt.userinfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.userinfo.dto.DriverWithdrawalLogDto;
import com.lcdt.userinfo.model.DriverWithdrawalLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DriverWithdrawalLogMapper extends BaseMapper<DriverWithdrawalLog> {
    List selectLogList(DriverWithdrawalLogDto driverWithdrawalDto);

    int updateWithdrawalStatus(@Param("withdrawalOutTradeNo") String outTradeNo, @Param("withdrawalCode") String orderNo, @Param("withdrawalStatus") int status);

    /**
     * 查询未落地的司机提现记录
     * @param driverWithdrawalLog
     * @return
     */
    List selectArray(DriverWithdrawalLog driverWithdrawalLog);

    DriverWithdrawalLog selectTotal(DriverWithdrawalLogDto driverWithdrawalDto);

    DriverWithdrawalLog selectBySerialNo(@Param("reqSeqNo") String reqSeqNo);

    List<DriverWithdrawalLog> queryList(@Param("withdrawalLog") DriverWithdrawalLog withdrawalLog);

}