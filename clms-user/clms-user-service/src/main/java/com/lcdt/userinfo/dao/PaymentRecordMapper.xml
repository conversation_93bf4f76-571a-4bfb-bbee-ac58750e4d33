<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.PaymentRecordMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.PaymentRecord">
    <id column="pr_id" jdbcType="BIGINT" property="prId" />
    <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount" />
    <result column="payee" jdbcType="VARCHAR" property="payee" />
    <result column="payee_id" jdbcType="BIGINT" property="payeeId" />
    <result column="payer" jdbcType="VARCHAR" property="payer" />
    <result column="payer_id" jdbcType="BIGINT" property="payerId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    pr_id, payment_amount, payee, payee_id, payer, payer_id, create_time, remark
  </sql>
</mapper>