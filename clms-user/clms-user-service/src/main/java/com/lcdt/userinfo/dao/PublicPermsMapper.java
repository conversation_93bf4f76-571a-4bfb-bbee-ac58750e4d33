package com.lcdt.userinfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.userinfo.model.PublicPerms;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PublicPermsMapper extends BaseMapper<PublicPerms> {

    /**
     * 分页列表
     *
     * @param page
     * @param publicPerms
     * @return
     */
    IPage<PublicPerms> selectMyPage(@Param("pg") Page<?> page, @Param("pp") PublicPerms publicPerms);

    /**
     * 查询所有的perms内容
     *
     * @return
     */
    List<String> selectAllPerms();
}