package com.lcdt.userinfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.userinfo.dto.BalanceRecordDto;
import com.lcdt.userinfo.model.BalanceRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BalanceRecordMapper extends BaseMapper<BalanceRecord> {

    List<BalanceRecord> selectByCondition(@Param("br") BalanceRecordDto balanceRecordDto);

    int selectConutByOrderNo(@Param("orderNo") String orderNo);

    int selectExportCount(@Param("br") BalanceRecordDto dto);

    List<BalanceRecord> selectExportDataLimit(@Param("br") BalanceRecordDto dto);

}