package com.lcdt.userinfo.web.controller.api;


import com.alibaba.fastjson2.JSONObject;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.userinfo.dto.BalanceRecordDto;
import com.lcdt.userinfo.model.BalanceRecord;
import com.lcdt.userinfo.model.CompanyCertificate;
import com.lcdt.userinfo.rpc.CompanyRpcService;
import com.lcdt.userinfo.service.BalanceRecordService;
import com.lcdt.userinfo.web.dto.AuthInfoDto;
import com.lcdt.userinfo.web.dto.PageResultDto;
import com.lcdt.util.ResponseJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
/**
 * 托运人账户管理相关api
 */
@RestController
@RequestMapping("/shipperbalance")
public class ShipperBalanceController {

    @Autowired
    private BalanceRecordService balanceRecordService;

    @Autowired
    SecurityInfoGetter securityInfoGetter;

    @Autowired
    private CompanyRpcService companyRpcService;


    /**
     * 账户余额流水列表
     *
     * @param balanceRecordDto 查询参数
     * @return 流水列表
     */
    @GetMapping("/recordlist")
    public PageResultDto balanceRecordList(BalanceRecordDto balanceRecordDto) {
        balanceRecordDto.setCompanyId(securityInfoGetter.getCompId());
        List<BalanceRecord> balanceRecords = balanceRecordService.getBalanceRecordList(balanceRecordDto);
        PageResultDto<BalanceRecord> balanceRecordPageResultDto = new PageResultDto<>(balanceRecords);
        return balanceRecordPageResultDto;
    }

    /**
     * 导出账户余额流水列表
     *
     * @param balanceRecordDto 查询参数
     * @return 导出结果
     */
    @PostMapping(value = "/export/record")
    public JSONObject exportRecord(BalanceRecordDto balanceRecordDto) {
        balanceRecordDto.setCompanyId(securityInfoGetter.getCompId());
        try{
            balanceRecordService.exportRecord(balanceRecordDto);
            return ResponseJsonUtils.successResponseJson("导出任务创建完成，请留意消息提醒！");
        }catch (Exception ex){
            return ResponseJsonUtils.failedResponseJson(ex.getMessage(),ex.getMessage());
        }
    }



    /**
     * 获取企业认证信息
     *
     * @return 认证信息
     */
    @GetMapping("/getAuthInfo")
    public JSONObject getAuthInfo() {
        CompanyCertificate companyCert = companyRpcService.getCompanyCert(securityInfoGetter.getCompId());
        AuthInfoDto authInfoDto = new AuthInfoDto();
        authInfoDto.setFullName(securityInfoGetter.getCompInfo().getFullName());
        authInfoDto.setIdentityName(companyCert.getIdentityName());
        authInfoDto.setIdentityPhone(companyCert.getIdentityPhone());
        authInfoDto.setAlipayNo(companyCert.getAlipayNo());
        authInfoDto.setIdCode(companyCert.getIdCode());
        return ResponseJsonUtils.successResponseJson(authInfoDto);
    }

}
