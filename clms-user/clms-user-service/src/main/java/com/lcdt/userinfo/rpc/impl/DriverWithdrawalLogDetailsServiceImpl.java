package com.lcdt.userinfo.rpc.impl;

import com.lcdt.userinfo.dao.DriverWithdrawalLogDetailsMapper;
import com.lcdt.userinfo.model.DriverWithdrawalLogDetail;
import com.lcdt.userinfo.rpc.DriverWithdrawalLogDetailsService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class DriverWithdrawalLogDetailsServiceImpl implements DriverWithdrawalLogDetailsService {

    @Autowired
    private DriverWithdrawalLogDetailsMapper driverWithdrawalLogDetailsMapper;

    @Override
    public void addDriverWithdrawalLogDetails(List<DriverWithdrawalLogDetail> driverWithdrawalLogDetails) {
        for (DriverWithdrawalLogDetail driverWithdrawalLogDetail : driverWithdrawalLogDetails) {
            driverWithdrawalLogDetailsMapper.insert(driverWithdrawalLogDetail);
        }
    }
}
