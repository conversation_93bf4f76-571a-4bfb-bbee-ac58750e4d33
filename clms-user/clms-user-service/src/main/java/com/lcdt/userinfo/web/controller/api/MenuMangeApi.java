package com.lcdt.userinfo.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.userinfo.dao.MenuMapper;
import com.lcdt.userinfo.dto.MenuDto;
import com.lcdt.userinfo.model.Menu;
import com.lcdt.userinfo.service.MenuManageService;
import com.lcdt.util.ResponseJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
/**
 * 菜单管理接口
 */
@RequestMapping("/menu")
@RestController
public class MenuMangeApi {

    @Autowired
    private MenuMapper menuMapper;

    @Autowired
    private MenuManageService menuManageService;

    /**
     * 菜单列表
     *
     * @param sysType 系统类型
     * @return 菜单列表
     */
    @GetMapping("/list")
    public JSONObject menuList(Integer sysType) {
        List<MenuDto> menuList = menuManageService.menuList(0L, sysType);
        if (!CollectionUtils.isEmpty(menuList)) {
            return ResponseJsonUtils.successResponseJson(menuList, "请求成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("请求失败");
        }

    }

    /**
     * 添加菜单
     *
     * @param menu 菜单信息
     * @return 添加结果
     */
    @PostMapping("/add")
    public JSONObject addMenu(Menu menu) {
        int rows = menuMapper.insert(menu);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("添加成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("添加失败");
        }

    }

    /**
     * 编辑菜单
     *
     * @param menu 菜单信息
     * @return 编辑结果
     */
    @PostMapping("/edit")
    public JSONObject editMenu(Menu menu) {
        int rows = menuMapper.updateById(menu);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("修改成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("修改失败");
        }

    }

    /**
     * 删除菜单
     *
     * @param menuId 菜单ID
     * @return 删除结果
     */
    @PostMapping("/del")
    public JSONObject delMenu(Long menuId) {
        int rows = menuManageService.delMenu(menuId);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("删除成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("删除失败");
        }

    }

    /**
     * 权限名称及对应的code
     *
     * @param sysType 系统类型
     * @return 权限列表
     */
    @GetMapping("/permsList")
    public JSONObject permsList(Integer sysType) {
        List<Menu> menuList = menuManageService.permsList(sysType);
        if (!CollectionUtils.isEmpty(menuList)) {
            return ResponseJsonUtils.successResponseJson(menuList, "请求成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("请求失败");
        }

    }
}
