package com.lcdt.userinfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lcdt.userinfo.dto.DriverAccountDto;
import com.lcdt.userinfo.model.DriverWallet;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface DriverWalletMapper extends BaseMapper<DriverWallet> {


    /**
     * 更新余额记录
     *
     * @param driverWalletId
     * @param addAmount
     * @return
     */
    int updateAmount(@Param("driverWalletId") Long driverWalletId, @Param("addAmount") Long addAmount);

    /**
     * 根据司机id更新余额
     *
     * @param driverWalletId
     * @param addAmount
     * @return
     */
    int updateAmountByDriverId(@Param("driverId") Long driverWalletId, @Param("addAmount") Long addAmount);

    /**
     * 更新冻结金额
     *
     * @param driverWalletId
     * @param amount
     * @return
     */
    int updateFrozenAmount(@Param("driverWalletId") Long driverWalletId, @Param("amount") Long amount);

    /**
     * 查询钱包表（关联司机表）
     *
     * @param page
     * @param dto
     * @return
     */
    IPage<DriverAccountDto> selectDriverAccount(@Param("page") IPage<?> page, @Param("dto") DriverAccountDto dto);

    /**
     * 更新农行余额
     *
     * @param driverWalletId
     * @param abcAmount
     * @return
     */
    int updateAbcAmountById(@Param("driverWalletId") Long driverWalletId, @Param("abcAmount") Long abcAmount);

    /**
     * 根据司机id累加农行余额
     *
     * @param driverId
     * @param abcAmount
     * @return
     */
    int updateAbcAmountByDriverId(@Param("driverId") Long driverId, @Param("amount") Long abcAmount);

    int updateAbcAmountByDriverIdAndAffiliatedPlatform(@Param("driverId") Long driverId, @Param("affiliatedPlatform") String affiliatedPlatform, @Param("amount") Long abcAmount);


}