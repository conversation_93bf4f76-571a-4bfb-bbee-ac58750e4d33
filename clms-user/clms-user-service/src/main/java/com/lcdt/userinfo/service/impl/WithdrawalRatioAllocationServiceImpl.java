package com.lcdt.userinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.userinfo.dao.WithdrawalRatioAllocationMapper;
import com.lcdt.userinfo.model.WithdrawalRatioAllocation;
import com.lcdt.userinfo.service.WithdrawalRatioAllocationService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

@Service
public class WithdrawalRatioAllocationServiceImpl implements WithdrawalRatioAllocationService {

    @Autowired
    private WithdrawalRatioAllocationMapper withdrawalRatioAllocationMapper;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Override
    public List<WithdrawalRatioAllocation> queryList(WithdrawalRatioAllocation withdrawalRatioAllocation) {
        PageHelper.startPage(withdrawalRatioAllocation.getPageNo(), withdrawalRatioAllocation.getPageSize());
        List<WithdrawalRatioAllocation> list =   withdrawalRatioAllocationMapper.queryList(withdrawalRatioAllocation);
        return list;
    }

    @Override
    public Integer insertWithdrawalRatioAllocation(WithdrawalRatioAllocation withdrawalRatioAllocation) {
        LambdaQueryWrapper<WithdrawalRatioAllocation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WithdrawalRatioAllocation::getAllocationCycleBegin,withdrawalRatioAllocation.getAllocationCycleBegin());
        lambdaQueryWrapper.eq(WithdrawalRatioAllocation::getAllocationCycleEnd,withdrawalRatioAllocation.getAllocationCycleEnd());
        lambdaQueryWrapper.eq(WithdrawalRatioAllocation::getAffiliatedPlatform,withdrawalRatioAllocation.getAffiliatedPlatform());
        lambdaQueryWrapper.eq(WithdrawalRatioAllocation::getEnabled,1);
        List<WithdrawalRatioAllocation> withdrawalRatioAllocations = withdrawalRatioAllocationMapper.selectList(lambdaQueryWrapper);
        withdrawalRatioAllocation.setCreateId(securityInfoGetter.getCompId());
        withdrawalRatioAllocation.setCreateDate(new Date());
        int insert = withdrawalRatioAllocationMapper.insert(withdrawalRatioAllocation);
        return insert;
    }

    @Override
    public Integer editWithdrawalRatioAllocation(WithdrawalRatioAllocation withdrawalRatioAllocation) {
        LambdaUpdateWrapper<WithdrawalRatioAllocation> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(WithdrawalRatioAllocation::getId,withdrawalRatioAllocation.getId());
        int update = withdrawalRatioAllocationMapper.update(withdrawalRatioAllocation, lambdaUpdateWrapper);
        return update;
    }
}
