<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.OrderShareRecordMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.OrderShareRecord">
        <id column="os_id" jdbcType="BIGINT" property="osId"/>
        <result column="merchant_id" jdbcType="VARCHAR" property="merchantId"/>
        <result column="relate_order_no" jdbcType="VARCHAR" property="relateOrderNo"/>
        <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo"/>
        <result column="share_order_no" jdbcType="VARCHAR" property="shareOrderNo"/>
        <result column="total_amount" jdbcType="BIGINT" property="totalAmount"/>
        <result column="share_status" jdbcType="INTEGER" property="shareStatus"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="callback_time" jdbcType="TIMESTAMP" property="callbackTime"/>
        <result column="share_type" jdbcType="INTEGER" property="shareType"/>
    </resultMap>
    <sql id="Base_Column_List">
        os_id
        , merchant_id, relate_order_no, out_trade_no, share_order_no, total_amount,
        share_status, create_time, callback_time, share_type
    </sql>

</mapper>