<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.DriverWithdrawalLogDetailsMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.DriverWithdrawalLogDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="record_id" jdbcType="BIGINT" property="recordId"/>
        <result column="waybill_id" jdbcType="BIGINT" property="waybillId"/>
        <result column="waybill_code" jdbcType="VARCHAR" property="waybillCode"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="interval_days" jdbcType="VARCHAR" property="intervalDays"/>
        <result column="waybill_fee" jdbcType="DECIMAL" property="waybillFee"/>
        <result column="withdrawal_allocation" jdbcType="VARCHAR" property="withdrawalAllocation"/>
        <result column="withdrawal_amount" jdbcType="DECIMAL" property="withdrawalAmount"/>
        <result column="commission" jdbcType="DECIMAL" property="commission"/>
        <result column="create_id" jdbcType="BIGINT" property="createId"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
    </resultMap>

</mapper>