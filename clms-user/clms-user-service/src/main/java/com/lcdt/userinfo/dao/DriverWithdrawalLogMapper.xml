<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.DriverWithdrawalLogMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.DriverWithdrawalLog">
        <id column="record_id" jdbcType="BIGINT" property="recordId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="withdrawal_name" jdbcType="VARCHAR" property="withdrawalName"/>
        <result column="withdrawal_code" jdbcType="VARCHAR" property="withdrawalCode"/>
        <result column="withdrawal_time" jdbcType="TIMESTAMP" property="withdrawalTime"/>
        <result column="withdrawal_amount" jdbcType="DECIMAL" property="withdrawalAmount"/>
        <result column="withdrawal_commission" jdbcType="DECIMAL" property="withdrawalCommission"/>
        <result column="withdrawal_remark" jdbcType="VARCHAR" property="withdrawalRemark"/>
        <result column="withdrawal_alipay_account" jdbcType="VARCHAR" property="withdrawalAlipayAccount"/>
        <result column="withdrawal_status" jdbcType="INTEGER" property="withdrawalStatus"/>
        <result column="withdrawal_out_trade_no" jdbcType="VARCHAR" property="withdrawalOutTradeNo"/>
        <result column="withdrawal_finish_time" jdbcType="TIMESTAMP" property="withdrawalFinishTime"/>
        <result column="serial_no" jdbcType="VARCHAR" property="serialNo"/>
        <result column="water_flag" jdbcType="VARCHAR" property="waterFlag"/>
        <result column="wait_flag" jdbcType="VARCHAR" property="waitFlag"/>
        <result column="affiliated_platform" jdbcType="VARCHAR" property="affiliatedPlatform"/>
    </resultMap>
    <select id="selectLogList" parameterType="com.lcdt.userinfo.dto.DriverWithdrawalLogDto" resultMap="BaseResultMap">
        select * from uc_driver_withdrawal_log t
        <where>
            t.driver_id = #{driverId,jdbcType=BIGINT}
            and t.affiliated_platform = #{affiliatedPlatform,jdbcType=VARCHAR}
            and t.withdrawal_status != 0
        </where>
        order by t.withdrawal_time desc
    </select>
    <update id="updateWithdrawalStatus" parameterType="com.lcdt.userinfo.dto.DriverWithdrawalLogDto">
        update uc_driver_withdrawal_log t
        set t.withdrawal_status = #{withdrawalStatus,jdbcType=INTEGER}
        <where>
            t.withdrawal_out_trade_no = #{withdrawalOutTradeNo,jdbcType=VARCHAR}
            and
            t.withdrawal_code = #{withdrawalCode,jdbcType=VARCHAR}

        </where>
    </update>

    <select id="selectArray" parameterType="com.lcdt.userinfo.model.DriverWithdrawalLog" resultMap="BaseResultMap">
        select * from uc_driver_withdrawal_log t
        <where>
            t.driver_id = #{driverId,jdbcType=BIGINT}
            and t.affiliated_platform = #{affiliatedPlatform,jdbcType=VARCHAR}
            and t.withdrawal_status = 1
            AND ( t.water_flag != 0 OR t.wait_flag != 0 )
        </where>
        order by t.withdrawal_time desc
    </select>
    <select id="selectTotal" resultType="com.lcdt.userinfo.model.DriverWithdrawalLog"
            parameterType="com.lcdt.userinfo.dto.DriverWithdrawalLogDto">
        select sum(t.withdrawal_amount) AS withdrawalAmount
        from uc_driver_withdrawal_log t
        where t.withdrawal_status = 2
        <if test="driverId!=null and driverId!=''">
            and t.driver_id =#{driverId}
        </if>
    </select>
    <select id="selectBySerialNo" resultType="com.lcdt.userinfo.model.DriverWithdrawalLog"
            parameterType="java.lang.String">
        select t.*
        from uc_driver_withdrawal_log t
        where t.serial_no = #{reqSeqNo,jdbcType=VARCHAR}
    </select>
    <select id="queryList" resultType="com.lcdt.userinfo.model.DriverWithdrawalLog"
            parameterType="com.lcdt.userinfo.model.DriverWithdrawalLog">
        select * from uc_driver_withdrawal_log
        where
        <if test="withdrawalLog.affiliatedPlatform!=null and withdrawalLog.affiliatedPlatform!=''">
            affiliated_platform = #{withdrawalLog.affiliatedPlatform,jdbcType=VARCHAR}
        </if>
        order by withdrawal_time desc
    </select>

</mapper>