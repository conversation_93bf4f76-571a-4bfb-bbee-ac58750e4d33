package com.lcdt.userinfo.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.userinfo.model.DriverWithdrawalLog;
import com.lcdt.userinfo.rpc.DriverWithdrawalLogService;
import com.lcdt.userinfo.web.dto.PageResultDto;
import com.lcdt.util.ResponseJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 提现记录接口
 */
@RestController
@RequestMapping("/driverWithdrawalLog")
public class DriverWithdrawalLogController {

    @Autowired
    private DriverWithdrawalLogService driverWithdrawalLogService;

    /**
     * 查询提现记录
     *
     * @param withdrawalLog 查询参数
     * @return 提现记录列表
     */
    @GetMapping("/list")
    public JSONObject withdrawalRatioAllocationList(DriverWithdrawalLog withdrawalLog) {
        List<DriverWithdrawalLog> withdrawalLogs = driverWithdrawalLogService.queryList(withdrawalLog);
        long sum = withdrawalLogs.stream().mapToLong(DriverWithdrawalLog::getWithdrawalAmount).sum();
        PageResultDto<DriverWithdrawalLog> balanceRecordPageResultDto = new PageResultDto<>(withdrawalLogs);
        JSONObject jsonObject = ResponseJsonUtils.successResponseJson(balanceRecordPageResultDto);
        jsonObject.put("sum", sum);
        return jsonObject;
    }

}
