package com.lcdt.userinfo.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lcdt.security.exception.CustomException;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.userinfo.dao.RoleMapper;
import com.lcdt.userinfo.dao.RolePermsMapper;
import com.lcdt.userinfo.dto.RoleDto;
import com.lcdt.userinfo.dto.RolePermsDto;
import com.lcdt.userinfo.model.Role;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.service.RoleManageService;
import com.lcdt.userinfo.web.dto.PageBaseDto;
import com.lcdt.userinfo.web.dto.PageResultDto;
import com.lcdt.util.ResponseJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 角色管理
 * <AUTHOR>
 */
@RequestMapping("/role")
@RestController
public class RoleManageApi {
    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private RoleManageService roleManageService;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private RolePermsMapper rolePermsMapper;

    /**
     * 角色分页列表
     */
    @GetMapping("/pagelist")
    public PageResultDto roleList(RoleDto role) {

        role.setCompanyId(securityInfoGetter.getCompId());

        List<RoleDto> roleDtos = roleMapper.selectMyPage(role);
        PageResultDto<RoleDto> roleDtoPageResultDto = new PageResultDto<>(roleDtos);
        return roleDtoPageResultDto;
    }

    /**
     * 角色列表
     * 无需传参，查询默认该用户企业下的所有角色
     */
    @GetMapping("/list")
    public JSONObject roleList() {
        List<Role> roleList = roleMapper.selectList(new QueryWrapper<Role>().lambda()
                .eq(Role::getCompanyId, securityInfoGetter.getCompId())
                .eq(Role::getValid, Short.parseShort("1")));
        return ResponseJsonUtils.successResponseJson(new PageBaseDto(roleList, roleList.size()));
    }

    /**
     * 添加角色
     * 只传角色名称即可,roleComment描述选填
     */
    @PostMapping("/add")
    public JSONObject addRole(Role role) {
        // 判断角色名称是否重复
        QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Role::getRoleName,role.getRoleName());
        queryWrapper.lambda().eq(Role::getCompanyId,securityInfoGetter.getCompId());
        Long count = roleMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new CustomException("角色名称不能重复");
        }
        role.setCompanyId(securityInfoGetter.getCompId());
        role.setValid(Short.parseShort("1"));
        User user = securityInfoGetter.getUserInfo();
        role.setCreateId(user.getUserId());
        role.setCreateName(user.getRealName());
        role.setCreateDate(new Date());
        int rows = roleMapper.insert(role);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("添加成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("添加失败");
        }

    }

    /**
     * 编辑角色
     * 必要参数：主键,其他参数根据实际情况填充
     */
    @PostMapping("/edit")
    public JSONObject editRole(Role role) {
        int rows = roleMapper.updateById(role);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("修改成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("修改失败");
        }

    }

    /**
     * 删除角色
     */
    @PostMapping("/del")
    public JSONObject delRole(Long roleId) {
        int rows = roleManageService.delRole(roleId);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("删除成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("删除失败");
        }

    }

    /**
     * 设置状态
     * 需要主键和状态两个参数
     */
    @PostMapping("/setstatus")
    public JSONObject setStatus(Role role) {
        role.setCompanyId(securityInfoGetter.getCompId());
        int rows = roleManageService.setStatus(role);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("设置成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("设置失败");
        }

    }


    /**
     * 设置权限
     */
    @PostMapping("/setPerms")
    public JSONObject setPerms(@RequestBody RolePermsDto rolePermsDto) {
        int rows = roleManageService.setPerms(rolePermsDto);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("设置成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("设置失败");
        }
    }
}