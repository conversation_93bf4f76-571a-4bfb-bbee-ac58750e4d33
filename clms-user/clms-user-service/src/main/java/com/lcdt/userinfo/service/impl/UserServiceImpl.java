package com.lcdt.userinfo.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.security.utils.SecurityUtils;
import com.lcdt.security.utils.StringUtils;
import com.lcdt.traffic.service.DriverRpcService;
import com.lcdt.userinfo.dao.UserMapper;
import com.lcdt.userinfo.dto.RegisterDto;
import com.lcdt.userinfo.event.RegisterUserEvent;
import com.lcdt.userinfo.exception.PhoneHasRegisterException;
import com.lcdt.userinfo.exception.UserNotExistException;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.model.UserCompRel;
import com.lcdt.userinfo.service.CompanyService;
import com.lcdt.userinfo.service.UserService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by ss on 2017/7/31.
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class UserServiceImpl implements UserService, ApplicationEventPublisherAware {

    private Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    private ApplicationEventPublisher applicationEventPublisher;//底层事件发布者

    @Autowired
    private UserMapper userMapper;

    @Autowired
    DriverRpcService driverRpcService;


    @Lazy
    @Autowired
    CompanyService companyService;


//    public boolean isUserAdmin(Long userId) {
//        return !CollectionUtils.isEmpty(adminUserMapper.selectByUserId(userId));
//    }


    @Override
    public User updateUser(User user) {
        userMapper.updateByPrimaryKeyWithoutPwd(user);
        //变更该用户所创建的企业的创建者手机号
        companyService.updateCompanyByCreateId(user.getPhone(), user.getUserId());
        return user;
    }

    @Override
    public User updateUserWechatOpenId(User user) {
        updateUser(user);
        return user;
    }

/*    @Override
    public User registerDriverUser(User user) {
		userMapper.insert(user);
		Driver driver = new Driver();
		driver.setDriverId(user.getUserId());
		driver.setDriverPhone(user.getPhone());
		driver.setDriverName(user.getNickName());
		driver.setDriverSource(2);
		driverRpcService.addDriver(driver);
		applicationEventPublisher.publishEvent(new RegisterUserEvent(driver));
        return user;
    }*/

    @Override
    public User updateUserWithpwd(User user) {
        userMapper.updateByPrimaryKey(user);
        return user;
    }


    /**
     * 锁思想 redis 分布式锁
     *
     * @param registerDto
     * @return
     * @throws PhoneHasRegisterException
     */
    @Transactional
    @Override
    public User registerUser(RegisterDto registerDto) throws PhoneHasRegisterException {
        boolean phoneBeenRegester = isPhoneBeenRegister(registerDto.getUserPhoneNum());
        if (phoneBeenRegester) {
            throw new PhoneHasRegisterException("手机号已被注册");
        }
        User registerUser = new User();
        registerUser.setPhone(registerDto.getUserPhoneNum());
        String md5EncryptPwd = SecurityUtils.encryptPassword(registerDto.getPassword());
        registerUser.setPwd(md5EncryptPwd);
        registerUser.setRealName(StringUtils.isEmpty(registerDto.getName()) ? registerDto.getUserPhoneNum() : registerDto.getName());
        registerUser.setReferrerPhone(registerDto.getIntroducer());
        registerUser.setRegisterDate(new Date());
        registerUser.setUserStatus((short) 1); //默认启用状态
        registerUser.setMobileCountry("86");
        registerUser.setEmail(registerDto.getEmail());
        registerUser.setLastLoginTime(registerUser.getRegisterDate());
        registerUser.setRegisterFrom(registerDto.getRegisterFrom());
        registerUser.setCompanyFullName(registerDto.getCompanyFullName());
        registerUser.setWechatOpenId(registerDto.getWechatOpenId());
        registerUser.setWechatOpenIdLogin(registerDto.getWechatOpenIdLogin());
        registerUser.setUserType(registerDto.getUserType());
        userMapper.insert(registerUser);
        //发布应用事件
        //applicationEventPublisher.publishEvent(new RegisterUserEvent(registerUser));
        return registerUser;
    }

    @Transactional
    @Override
    public JSONObject registerOrLogin(RegisterDto registerDto) throws PhoneHasRegisterException {
        User user = userMapper.queryByUserPhone(registerDto.getUserPhoneNum());
        if (null == user) {//该手机号需要注册
            User registerUser = new User();
            registerDto.setRegisterFrom("管车宝小程序");
            registerUser.setPhone(registerDto.getUserPhoneNum());
//			String md5EncryptPwd = RegisterUtils.md5Encrypt(registerDto.getPassword());
//			registerUser.setPwd(md5EncryptPwd);
//			registerUser.setRealName(registerDto.getName());
//			registerUser.setReferrerPhone(registerDto.getIntroducer());
            registerUser.setRegisterDate(new Date());
            registerUser.setUserStatus((short) 1); //默认启用状态
            registerUser.setMobileCountry("86");
//			registerUser.setEmail(registerDto.getEmail());
            registerUser.setLastLoginTime(new Date());
//			registerUser.setNickName(registerDto.getName());
//			registerUser.setRegisterFrom(registerDto.getRegisterFrom());
//			registerUser.setCompanyFullName(registerDto.getCompanyFullName());
            userMapper.insert(registerUser);
            //发布应用事件
            applicationEventPublisher.publishEvent(new RegisterUserEvent(registerUser));
            HashMap<String, Object> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put("userId", registerUser.getUserId());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("user", registerUser);
            jsonObject.put("code", 0);
//            jsonObject.put("token", jwtTokenUtil.generateToken(stringStringHashMap));
            return jsonObject;
        } else {//该手机号已存在，需要登录
            HashMap<String, Object> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put("userId", user.getUserId());
            Date date = new Date();
//            Calendar instance = Calendar.getInstance();
//            instance.setTime(DateUtility.getDateAfterDays(date, 36500));
//			String s = jwtTokenUtil.generateToken(stringStringHashMap,instance.getTime());
            List<UserCompRel> userCompRels = companyService.companyList(user.getUserId());
            JSONObject jsonObject = new JSONObject();
//			jsonObject.put("token", s);
            jsonObject.put("user", user);
            jsonObject.put("comps", userCompRels);
            return jsonObject;
        }
    }


    @Transactional(readOnly = true)
    @Override
    public boolean isPhoneBeenRegister(String phone) {
        User user = userMapper.queryByUserPhone(phone);
        return user == null ? false : true;
    }


    @Transactional(readOnly = true)
    public User isPhoneRegister(String phone) {
        User user = userMapper.queryByUserPhone(phone);
        return user;
    }

    @Transactional(readOnly = true)
    @Override
    public User selectUserByPhone(String phone) {
        return userMapper.queryByUserPhone(phone);
    }

    @Transactional(readOnly = true)
    @Override
    public User selectByUnionId(String unionId) {
        final List<User> users = userMapper.selectByWxUnionId(unionId);
        if (users.isEmpty()) {
            return null;
        }
        return users.iterator().next();
    }

    @Override
    public User resetPwd(String username, String pwd) {
        //加密方式修改 by xuerr
        String encrypt = SecurityUtils.encryptPassword(pwd);
        User user = userMapper.queryByUserPhone(username);
        user.setPwd(encrypt);
        userMapper.updateByPrimaryKey(user);
        return user;
    }


    @Override
    public boolean checkUserLogin() {
        return false;
    }

    @Transactional(readOnly = true)
    @Override
    public User queryByUserId(Long userId) throws UserNotExistException {
        User user = userMapper.selectByPrimaryKey(userId);
        if (user == null) {
            throw new UserNotExistException();
        }
        return user;
    }

    @Transactional(readOnly = true)
    @Override
    public User queryByPhone(String phone) throws UserNotExistException {
        User user = userMapper.queryByUserPhone(phone);
        return user;
    }


    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }


    @Transactional(readOnly = true)
    @Override
    public User selectByUnionIdLogin(String unionId) {
        final List<User> users = userMapper.selectByUnionIdLogin(unionId);
        if (users.isEmpty()) {
            return null;
        }
        return users.iterator().next();
    }

    @Override
    public int deleteByUserId(Long userId) {
        return userMapper.deleteByPrimaryKey(userId);
    }

    @Override
    public int deleteByUserIdAndCompanyId(Long userId, Long companyId) {
        return userMapper.deleteByUserIdAndCompanyId(userId, companyId);
    }

}
