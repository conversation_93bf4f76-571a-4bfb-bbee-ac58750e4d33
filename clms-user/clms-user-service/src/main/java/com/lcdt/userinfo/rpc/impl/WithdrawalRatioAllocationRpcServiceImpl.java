package com.lcdt.userinfo.rpc.impl;

import com.lcdt.userinfo.dao.WithdrawalRatioAllocationMapper;
import com.lcdt.userinfo.model.WithdrawalRatioAllocation;
import com.lcdt.userinfo.rpc.WithdrawalRatioAllocationRpcService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
@Transactional
public class WithdrawalRatioAllocationRpcServiceImpl implements WithdrawalRatioAllocationRpcService {

    @Autowired
    private WithdrawalRatioAllocationMapper withdrawalRatioAllocationMapper;

    @Override
    public List<WithdrawalRatioAllocation> selectWithdrawalRatioAllocationByaffiliatedPlatform(String affiliatedPlatform) {
        WithdrawalRatioAllocation withdrawalRatioAllocation = new WithdrawalRatioAllocation();
        withdrawalRatioAllocation.setAffiliatedPlatform(affiliatedPlatform);
        List<WithdrawalRatioAllocation> withdrawalRatioAllocations = withdrawalRatioAllocationMapper.queryList(withdrawalRatioAllocation);
        return withdrawalRatioAllocations;
    }
}
