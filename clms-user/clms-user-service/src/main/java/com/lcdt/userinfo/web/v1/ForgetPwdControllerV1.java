package com.lcdt.userinfo.web.v1;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.notify.rpcservice.ValidCodeService;
import com.lcdt.userinfo.exception.UserNotExistException;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.service.UserService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

/**
 * <AUTHOR>
 * @create 2020-01-08-15:32
 **/
@RestController
@RequestMapping("/forgetpwd")
public class ForgetPwdControllerV1 {

    private static final String SESSIONKEY = "forgetpwd_phone";
    private String validcodeTag = "forgetpwd";

    @Autowired
    UserService userService;
    @Autowired
    private ValidCodeService validCodeService;
    @Autowired
    HttpServletRequest request;

    @RequestMapping("/checkvalidcode/v1")
    public JSONObject checkValidCode(String validcode, String phoneNum) throws UserNotExistException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", -1);
        User user = userService.queryByPhone(phoneNum);
        if (user == null) {
            jsonObject.put("message", "没有该账号！");
            return jsonObject;
        }

        boolean codeCorrect = validCodeService.isCodeCorrect(validcode, validcodeTag, phoneNum);

//        if (true) {
        if (codeCorrect) {
            //如果验证码正确
            jsonObject.put("code", 0);
            return jsonObject;
        } else {
            jsonObject.put("message", "验证码错误！");
            return jsonObject;
        }
    }

    @RequestMapping("/setnewpwd/v1")
    public JSONObject setNewPwd(String pwd) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", -1);
        HttpSession session = request.getSession(false);
        Object attribute = session.getAttribute(SESSIONKEY);
        if (StringUtils.isEmpty(pwd)) {
            jsonObject.put("message", "密码不能为空！");
            return jsonObject;
        }
        if (attribute == null) {
            jsonObject.put("message", "请重新接收验证码！");
            return jsonObject;
        }
        String phone = (String) attribute;
        userService.resetPwd(phone, pwd);
        jsonObject.put("code", 0);
        return jsonObject;
    }
}
