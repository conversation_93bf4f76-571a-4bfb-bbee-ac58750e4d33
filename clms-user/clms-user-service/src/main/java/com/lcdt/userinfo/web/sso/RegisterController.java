package com.lcdt.userinfo.web.sso;


import com.alibaba.fastjson2.JSONObject;
import com.lcdt.security.constant.Constants;
import com.lcdt.security.exception.BaseException;
import com.lcdt.security.security.JwtLoginService;
import com.lcdt.security.utils.IdUtils;
import com.lcdt.security.utils.VerifyCodeUtils;
import com.lcdt.common.component.RedisCache;
import com.lcdt.notify.rpcservice.NotifyService;
import com.lcdt.notify.rpcservice.ValidCodeService;
import com.lcdt.userinfo.dto.RegisterDto;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * edit date 2022-07-01
 *
 * <AUTHOR>
 */
/**
 * 托运人/承运人注册接口
 */
@RestController
@RequestMapping("/auth/register")
public class RegisterController {

    @Autowired
    UserService userService;

    @Autowired
    NotifyService notifyService;

    @Autowired
    private ValidCodeService validCodeService;

    private static final String VALID_CODE_TAG = "register";

    @Autowired
    private RedisCache redisCache;


    @Autowired
    private JwtLoginService jwtLoginService;


    /***
     * 注册提交
     * @param registerDto
     * @return
     */
    /**
     * 注册
     *
     * @param registerDto 注册信息
     * @param request HTTP请求
     * @return 注册结果
     */
    @PostMapping(path = "/save")
    @ResponseBody
    public Map toSave(@Valid RegisterDto registerDto, HttpServletRequest request) {
        Map<String, Object> map = new HashMap();
        String msg = "";
        int flag = -1;
        boolean codeCorrect = validCodeService.isCodeCorrect(registerDto.getEcode(), VALID_CODE_TAG, registerDto.getUserPhoneNum());
        if (!codeCorrect) {
            msg = "验证码错误！";
        }

        if (!registerDto.getPassword().equals(registerDto.getPassword1())) {
            msg = "两次输入密码不一样！";
        }
        if (userService.isPhoneBeenRegister(registerDto.getUserPhoneNum())) { //根据用户首页号检查用户是否有注册
            msg = "手机号" + registerDto.getUserPhoneNum() + "已注册！";
        }
        if (msg == "") {
            try {
                // registerDto.setRegisterFrom("web主站");
                User fUser = userService.registerUser(registerDto);
                //LoginSessionReposity.setUserInSession(request, fUser);
                if (fUser != null) {
                    //注册成功逻辑
                    String verifyCode = VerifyCodeUtils.generateVerifyCode(4);
                    String uuid = IdUtils.simpleUUID();
                    String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
                    redisCache.setCacheObject(verifyKey, verifyCode, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
                    String token = jwtLoginService.login(registerDto.getUserPhoneNum(), registerDto.getPassword(), verifyCode, uuid, registerDto.getUserType().intValue());        // 生成令牌
                    map.put(Constants.TOKEN, token);
                    flag = 0;
                } else {
                    msg = "注册失败！";
                }
            } catch (Exception e) {
                throw new BaseException(msg);
            }
        }
        map.put("message", msg);
        map.put("code", flag);
        return map;
    }

    /***
     * 发信短信码
     * @return
     */
    /**
     * 获取验证码
     *
     * @param request HTTP请求
     * @param phone 手机号
     * @return 发送结果
     */
    @GetMapping(path = "/getSmsCode")
    public JSONObject getSmsCode(HttpServletRequest request, String phone) {
        String msg = "";
        int flag = -1;
        if (userService.isPhoneBeenRegister(phone)) { //根据用户首页号检查用户是否有注册
            msg = "手机号" + phone + "已注册，请选用别的号注册！";
        } else {
            validCodeService.sendValidCode(VALID_CODE_TAG, 60 * 5, phone);
            msg = "发送成功";
            flag = 0;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("message", msg);
        jsonObject.put("code", flag);
        return jsonObject;
    }

}
