package com.lcdt.userinfo.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.userinfo.dto.GroupQueryDto;
import com.lcdt.userinfo.model.Group;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.rpc.GroupRpcService;
import com.lcdt.userinfo.web.dto.PageBaseDto;
import com.lcdt.util.ResponseJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by ybq on 2020/9/7 11:44
 */
/**
 * 托运人-项目组管理接口
 */
@RestController
@RequestMapping("/shipper-group")
public class ShipperGroupController {

    @Autowired
    private GroupRpcService groupRpcService;


    @Autowired
    private SecurityInfoGetter securityInfoGetter;


    /**
     * 列表
     *
     * @param dto 查询参数
     * @return 项目组列表
     */
    @GetMapping(value = "/list")
    public JSONObject list(GroupQueryDto dto) {
        dto.setCompanyId(securityInfoGetter.getCompId());
        PageInfo pg = groupRpcService.groupList(dto);
        if (CollectionUtils.isEmpty(pg.getList())) {
            pg.setTotal(0);
        }
        return ResponseJsonUtils.successResponseJson(new PageBaseDto(pg.getList(), pg.getTotal()));
    }


    /**
     * 保存
     *
     * @param group 项目组信息
     * @return 保存结果
     */
    @PostMapping(value = "/create")
    public JSONObject create(Group group) {
        group.setCompanyId(securityInfoGetter.getCompId());
        int result = groupRpcService.addGroup(group);
        if (result > 0) {
            return ResponseJsonUtils.successResponseJson("操作成功！");
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("保存失败！");
    }

    /**
     * 修改
     *
     * @param group 项目组信息
     * @return 修改结果
     */
    @PostMapping(value = "/modify")
    public JSONObject modifyGroup(Group group) {
        group.setCompanyId(securityInfoGetter.getCompId());
        int result = groupRpcService.modifyGroup(group);
        if (result > 0) {
            return ResponseJsonUtils.successResponseJson("操作成功！");
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("操作失败！");
    }


    /**
     * 启停 0-停止/ 1-开启
     *
     * @param groupId 项目组ID
     * @param flag 启停标志
     * @return 操作结果
     */
    @PostMapping(value = "/startStop")
    public JSONObject startStop(Long groupId, int flag) {
        Group group = new Group();
        group.setCompanyId(securityInfoGetter.getCompId());
        group.setGroupId(groupId);
        group.setStatus(flag);
        int result = groupRpcService.startStop(group);
        if (result > 0) {
            return ResponseJsonUtils.successResponseJson("操作成功！");
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("操作失败！");
    }

    /**
     * 用户组列表
     *
     * @param status 状态
     * @return 用户组列表
     */
    @GetMapping(value = "/user-group-list")
    public JSONObject userGroupList(Integer status) {
        User user = securityInfoGetter.getUserInfo();
        if (user.getIsSub() == 1) { //如果是主账号
            GroupQueryDto dto = new GroupQueryDto();
            if (!ObjectUtils.isEmpty(status)) {
                dto.setStatus(status);
            }
            dto.setCompanyId(securityInfoGetter.getCompId());
            List<Group> groups = groupRpcService.groupAllList(dto);
            return ResponseJsonUtils.successResponseJson(groups);
        }
        return ResponseJsonUtils.successResponseJson(groupRpcService.groupList4UserId(user.getUserId(),
                securityInfoGetter.getCompId(), status));
    }


    /**
     * 所有组列表
     *
     * @return 所有组列表
     */
    @GetMapping(value = "/group-all-list")
    public JSONObject groupAllList() {
        GroupQueryDto dto = new GroupQueryDto();
        dto.setCompanyId(securityInfoGetter.getCompId());
        return ResponseJsonUtils.successResponseJson(groupRpcService.groupAllList(dto));
    }


}
