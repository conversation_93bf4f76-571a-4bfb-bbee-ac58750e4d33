package com.lcdt.userinfo.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lcdt.userinfo.dao.SuperBankMapper;
import com.lcdt.userinfo.model.SuperBank;
import com.lcdt.userinfo.rpc.SuperBankRpcService;
import com.lcdt.util.CheckEmptyUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class SuperBankRpcServiceImpl implements SuperBankRpcService {

    @Autowired
    private SuperBankMapper superBankMapper;

    @Override
    public SuperBank getName(String bankName) {
        LambdaQueryWrapper<SuperBank> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.like(SuperBank::getBankName,bankName);
        lambdaQueryWrapper.eq(SuperBank::getStatus,1);
        List<SuperBank> superBanks = superBankMapper.selectList(lambdaQueryWrapper);
        if(CheckEmptyUtil.isNotEmpty(superBanks) && superBanks.size() == 1){
            return superBanks.get(0);
        }else {
            throw new RuntimeException("银行名称错误或未暂不支持该银行卡");
        }
    }
}
