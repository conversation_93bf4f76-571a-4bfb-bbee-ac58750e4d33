package com.lcdt.userinfo.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.lcdt.security.exception.CustomException;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.common.config.SettingProperties;
import com.lcdt.traffic.service.DriverRpcService;
import com.lcdt.traffic.service.EvaluationService;
import com.lcdt.traffic.service.VehicleService;
import com.lcdt.userinfo.dto.*;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.model.CompanyCertificate;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.rpc.CompanyRpcService;
import com.lcdt.userinfo.rpc.IAuthenRpcService;
import com.lcdt.userinfo.service.CompanyService;
import com.lcdt.userinfo.web.dto.PageBaseDto;
import com.lcdt.util.BeanProperties;
import com.lcdt.util.ResponseJsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by ybq on 2020/10/20 11:49
 */
@RestController
@Slf4j
/**
 * 运营端客户接口
 */
@RequestMapping("/api/carrier-customer")
public class CarrierCustomerController {

    @Autowired
    private IAuthenRpcService iAuthenRpcService;

    @Autowired
    private EvaluationService evaluationService;


    @Autowired
    private CompanyService companyService;


    @Autowired
    private DriverRpcService driverRpcService;

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private CompanyRpcService companyRpcService;

    @Autowired
    SecurityInfoGetter securityInfoGetter;

    @Autowired
    private SettingProperties settingProperties;


    /**
     * 托运人认证、列表
     *
     * @param dto 查询参数
     * @return 认证列表
     */
    @GetMapping("/auth-list")
    public JSONObject list(AuthenticationQueryDto dto) {
        if (dto.getPageNo() == null) {
            dto.setPageNo(1);
        }
        if (dto.getPageSize() == null) {
            dto.setPageSize(10);
        }
        dto.setUserType(1);
        PageInfo pg = iAuthenRpcService.authencationList(dto);
        HashMap m1 = new HashMap();
        m1.put("list", pg.getList());
        m1.put("total", pg.getTotal());

        return ResponseJsonUtils.successResponseJson(m1);
    }

    /**
     * 托运人管理
     *
     * @param pageNo     页码
     * @param pageSize   每页条数
     * @param fullName   企业名称
     * @param enterStatus 状态
     * @return 托运人列表
     */
    @GetMapping("/shipperManage")
    public JSONObject shipperManagelist(@RequestParam(defaultValue = "1") Long pageNo,
                                        @RequestParam(defaultValue = "10") Long pageSize, String fullName, Integer enterStatus) {
        IPage<CompanyCertDto> iPage = iAuthenRpcService.selectShipper4AccountManage(new Page<>(pageNo, pageSize), fullName, enterStatus);
        return ResponseJsonUtils.successResponseJson(new PageBaseDto(iPage.getRecords(), iPage.getTotal()));
    }


    /**
     * 企业详细
     *
     * @param companyId 企业ID
     * @return 企业详情
     */
    @GetMapping("/auth-detail4company")
    public JSONObject detail4company(Long companyId) {
        Company company = iAuthenRpcService.companyDetail(companyId);
        List<CompanyCertificate> companyCertificateList = iAuthenRpcService.companyCertificateList(companyId);
        Map map = evaluationService.getAverageScore(companyId, new Long("1"));
        if (!ObjectUtils.isEmpty(company)) {
            HashMap m1 = new HashMap();
            m1.put("company", company);
            m1.put("companycertificateList", companyCertificateList);
            m1.put("evaluation", map);
            return ResponseJsonUtils.successResponseJson(m1);
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("获取数据不存在");
        }
    }

    /**
     * 认证详细
     *
     * @param companyId 企业ID
     * @return 认证详情
     */
    @GetMapping("/detail4authencation")
    public JSONObject detail4authencation(Long companyId) {
        List<CompanyCertificate> companyCertificateList = iAuthenRpcService.companyCertificateList(companyId);
        if (null != companyCertificateList) {
            return ResponseJsonUtils.successResponseJson(companyCertificateList);
        }
        return ResponseJsonUtils.failedResponseJsonWithoutData("获取数据不存在");
    }


    /**
     * 获取评价总平均数
     *
     * @param companyId 企业ID
     * @return 评价结果
     */
    @GetMapping("/detail4averagescore")
    public JSONObject detail4averagescore(Long companyId) {
        HashMap m1 = new HashMap();
        m1.put("detail", evaluationService.getAverageScore(companyId, new Long("1")));
        return ResponseJsonUtils.successResponseJson(m1);
    }


    /**
     * 修改企业信息
     *
     * @param modifyCompanyInfoDto 企业信息
     * @return 修改后的企业信息
     */
    @PostMapping(value = "/updatecompamy")
    public Company updatecompamy(@Validated ModifyCompanyInfoDto modifyCompanyInfoDto) {

        // 整体更新分为两个部分 1.更新企业主表信息  2.更新企业认证信息表
        User loginUser = securityInfoGetter.getUserInfo();

        Company company = companyService.selectById(modifyCompanyInfoDto.getCompanyId());
        company.setShortName(modifyCompanyInfoDto.getShortName());
        if (!StringUtils.isEmpty(modifyCompanyInfoDto.getFullName()) &&
                !modifyCompanyInfoDto.getFullName().equals(company.getFullName())) {
            CompanyDto companyDto = new CompanyDto();
            companyDto.setCompanyName(modifyCompanyInfoDto.getFullName());
            Company company1 = companyService.findCompany(companyDto);
            if (company1 != null) {
                throw new CustomException("企业名称已经存在!");
            }
        }
        company.setFullName(modifyCompanyInfoDto.getFullName());
        company.setIndustry(modifyCompanyInfoDto.getIndustry());
        company.setProvince(modifyCompanyInfoDto.getProvince());
        company.setCity(modifyCompanyInfoDto.getCity());
        company.setCounty(modifyCompanyInfoDto.getCounty());
        company.setDetailAddress(modifyCompanyInfoDto.getDetailAddress());
        company.setCompIntro(modifyCompanyInfoDto.getCompIntro());
        company.setIsOverload(modifyCompanyInfoDto.getIsOverload());
        company.setLinkMan(modifyCompanyInfoDto.getLinkMan());
        company.setLinkTel(modifyCompanyInfoDto.getLinkTel());
        company.setAffiliatedPlatform(modifyCompanyInfoDto.getAffiliatedPlatform());
        companyService.updateCompany(company);
        // 更新认证表信息，注意重置网商关于图片的id
        CompanyCertificate companyCertificate = new CompanyCertificate();
        companyCertificate.setYingyezhizhao(modifyCompanyInfoDto.getYingyezhizhao());
        companyCertificate.setYingyezhizhaoId("");
        companyCertificate.setSocialCreditCode(modifyCompanyInfoDto.getSocialCreditCode());
        companyCertificate.setIdentityFront(modifyCompanyInfoDto.getIdentityFront());
        companyCertificate.setIdentityFrontId("");
        companyCertificate.setIdentityBack(modifyCompanyInfoDto.getIdentityBack());
        companyCertificate.setIdentityBackId("");
        companyCertificate.setRemark(modifyCompanyInfoDto.getRemark());
        companyCertificate.setIdentityName(modifyCompanyInfoDto.getIdentityName());
        companyCertificate.setIdCode(modifyCompanyInfoDto.getIdCode());
        companyCertificate.setCertiId(modifyCompanyInfoDto.getCertiId());
        companyService.updateCompanyCertInfo(companyCertificate);
        return company;
    }

    /**
     * 认证审核
     *
     * @param dto 审核参数
     * @return 审核结果
     */
    @PostMapping(value = "/updateauth")
    public JSONObject updateauth(@Validated ModifyCompanyAuthDto dto) {
        try {
            User loginUser = securityInfoGetter.getUserInfo();
            CompanyCertificate companyCertificate = companyService.queryCertByCompanyId(dto.getCompId());
            if (companyCertificate == null) {
                companyCertificate = new CompanyCertificate();
                companyCertificate.setCompId(dto.getCompId());
            }
            BeanProperties.copyProperties(dto, companyCertificate, null, null);
            companyService.updateCompanyCert(companyCertificate, dto.getAuthentication());
            Company company = new Company();
            company.setCompId(dto.getCompId());
            company.setFullName(dto.getFullName());
            company.setAffiliatedPlatform(dto.getAffiliatedPlatform());
            companyService.updateCompanyByCompId(company);
            //客户提交快货运
            Company oo = companyService.selectById(company.getCompId());
            String uploadPlatform = settingProperties.getUploadPlatform();
            CompanyCertificate finalCompanyCertificate = companyCertificate;
//            try {
//                if (com.lcdt.traffic.constants.Constants.KHY.equalsIgnoreCase(uploadPlatform)) {
//                        companyService.uploadCompanyToKhy(finalCompanyCertificate, oo, loginUser.getUserId());
//                } else if (Constants.TJSW.contains(uploadPlatform)) {
//                    Company comp = companyService.selectById(dto.getCompId());
//                    companyService.uploadCompanyToTjsw(finalCompanyCertificate, comp, loginUser.getUserId());
//                }
//            } catch (Exception ex) {
//                logger.error("公司上传快货运平台error", ex);
//            }
            return ResponseJsonUtils.successResponseJsonWithoutData("审核成功");
        } catch (Exception e) {
            log.error("认证审核失败:", e);
            return ResponseJsonUtils.failedResponseJsonWithoutData("审核操作失败：" + e.getMessage());
        }
    }


    /**
     * 用户企业启停
     *
     * @param dto 启停参数
     * @return 操作结果
     */
    @PostMapping(value = "/toggleenableuser")
    public JSONObject toggleenableuser(@Validated ToggleCompanyEnableDto dto) {
        Company company = iAuthenRpcService.toggleEnableCompany(dto);
        if (ObjectUtils.isEmpty(company)) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("更新失败");
        }
        return ResponseJsonUtils.successResponseJsonWithoutData("操作成功");
    }


    /**
     * 待审核数量
     *
     * @return 待审核数量统计
     */
    @GetMapping("/waitting-audit-count")
    public JSONObject waitAuditCount() {
        HashMap m1 = new HashMap();
        m1.put("shipperCount", authenticationStat(1, (short) 1,1)); // 托运人 认证中
//        m1.put("driverCount", driverRpcService.queryDriverCount(1)); // 司机认证中
//        m1.put("vehicleCount", vehicleService.getUnauthCount(1)); // 车辆认证中
        m1.put("driverCount", 0); // 司机认证中
        m1.put("vehicleCount", 0); // 车辆认证中
        m1.put("captainCount", authenticationStat(1, (short) 1,3)); // 托运人 认证中
        return ResponseJsonUtils.successResponseJson(m1);
    }

    private Long authenticationStat(Integer userType, short authenStatus ,Integer companyType) {
        AuthenticationQueryDto dto = new AuthenticationQueryDto();
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setUserType(userType);
        dto.setCompanyType(companyType);
        dto.setAuthentication(authenStatus);//提交认证
        PageInfo pg = iAuthenRpcService.authencationList(dto);
        return pg.getTotal();
    }


}
