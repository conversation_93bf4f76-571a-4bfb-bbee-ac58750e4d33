package com.lcdt.userinfo.web;

import com.lcdt.userinfo.model.CarrierBalanceRecord;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RQueue;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * Redisson测试
 *
 * <AUTHOR>
 * @date 2021-08-11
 */
@RestController
@RequestMapping("/auth")
@Slf4j
public class RedissonTestController {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * redisson锁
     *
     * @return
     */
    @GetMapping("/redisson")
    public String testRedisson() {
        //获取分布式锁，只要锁的名字一样，就是同一把锁
        RLock lock = redissonClient.getLock("redisson-lock");
        boolean b = false;
        //加锁（阻塞等待），默认过期时间是30秒
        try {
            long start = System.currentTimeMillis();
            b = lock.tryLock(0, 10, TimeUnit.SECONDS);
            if (b) {
                //如果业务执行过长，Redisson会自动给锁续期


                log.info("业务开始执行>>>");
                Thread.sleep(3000);
                log.info("<<<执行完毕");
                lock.unlock();
            } else {
                log.error("超时了---");
                return "超时了";
            }
            long tm = (System.currentTimeMillis() - start) / 1000;
            return "执行耗时：" + tm + "秒";
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            log.info("----finally");
            //解锁，如果业务执行完成，就不会继续续期，即使没有手动释放锁，在30秒过后，也会释放锁
//            lock.unlock();
        }
        return "Hello Redisson!";
    }

    private int i = 1;

    /**
     * redisson消息队列
     *
     * @return
     */
    @GetMapping("/topic")
    public String testTopic() {
        CarrierBalanceRecord carrierBalanceRecord = new CarrierBalanceRecord();
        carrierBalanceRecord.setOutTradeNo((i++) + "");
        carrierBalanceRecord.setRelateOrderNo("2222222222");

        try {

            RTopic topic1 = redissonClient.getTopic("carrierBalanceRecord");
            topic1.publish(carrierBalanceRecord);
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
        return "ss";
    }

    /**
     * redisson消息队列
     *
     * @return
     */
    @GetMapping("/queue")
    public String testQueue() {
        CarrierBalanceRecord carrierBalanceRecord = new CarrierBalanceRecord();
        carrierBalanceRecord.setOutTradeNo((i++) + "");
        carrierBalanceRecord.setRelateOrderNo("2222222222");
        RQueue<CarrierBalanceRecord> queue = redissonClient.getQueue("anyQueue");//定义个队列
        queue.add(carrierBalanceRecord);
//        SomeObject obj = queue.peek(); //检查
//        SomeObject someObj = queue.poll();//取值

        return "ss";
    }
}
