<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.DriverWalletMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.DriverWallet">
        <id column="driver_wallet_id" jdbcType="BIGINT" property="driverWalletId"/>
        <result column="driver_id" jdbcType="BIGINT" property="driverId"/>
        <result column="driver_enter_status" jdbcType="INTEGER" property="driverEnterStatus"/>
        <result column="driver_enter_remark" jdbcType="VARCHAR" property="driverEnterRemark"/>
        <result column="driver_merchant_id" jdbcType="INTEGER" property="driverMerchantId"/>
        <result column="driver_open_pay" jdbcType="INTEGER" property="driverOpenPay"/>
        <result column="driver_wallet_ balance" jdbcType="BIGINT" property="driverWalletBalance"/>
        <result column="driver_wallet_amount" jdbcType="BIGINT" property="driverWalletAmount"/>
        <result column="frozen_amount" jdbcType="BIGINT" property="frozenAmount"/>
        <result column="driver_alipay_account" jdbcType="VARCHAR" property="driverAlipayAccount"/>
        <result column="driver_out_merchant_id" jdbcType="VARCHAR" property="driverOutMerchantId"/>
        <result column="driver_order_no" jdbcType="VARCHAR" property="driverOrderNo"/>
        <result column="driver_branch_no" jdbcType="VARCHAR" property="driverBranchNo"/>
        <result column="driver_branch_name" jdbcType="VARCHAR" property="driverBranchName"/>
        <result column="driver_bank_card_no" jdbcType="VARCHAR" property="driverBankCardNo"/>
        <result column="driver_bank_cert_name" jdbcType="VARCHAR" property="driverBankCertName"/>
        <result column="driver_bank_card_no_self" jdbcType="VARCHAR" property="driverBankCardNoSelf"/>
        <result column="driver_branch_name_self" jdbcType="VARCHAR" property="driverBranchNameSelf"/>
        <result column="driver_branch_province" jdbcType="VARCHAR" property="driverBranchProvince"/>
        <result column="driver_branch_city" jdbcType="VARCHAR" property="driverBranchCity"/>
        <result column="driver_branch_county" jdbcType="VARCHAR" property="driverBranchCounty"/>
        <result column="driver_ant_signed" jdbcType="INTEGER" property="driverAntSigned"/>
        <result column="ab_no" jdbcType="VARCHAR" property="abNo"/>
        <result column="is_abc_bank" jdbcType="INTEGER" property="isAbcBank"/>
        <result column="abc_balance" jdbcType="BIGINT" property="abcBalance"/>
        <result column="driver_open_bank_code" jdbcType="VARCHAR" property="driverOpenBankCode"/>
        <result column="driver_open_bank_name" jdbcType="VARCHAR" property="driverOpenBankName"/>
        <result column="recever_phone" jdbcType="VARCHAR" property="receverPhone"/>
        <result column="affiliated_platform" jdbcType="VARCHAR" property="affiliatedPlatform"/>
    </resultMap>
    <update id="updateAmount" parameterType="java.lang.Long">
        UPDATE uc_driver_wallet
        <set>
            driver_wallet_amount = driver_wallet_amount + #{addAmount}
        </set>
        WHERE driver_wallet_id = #{driverWalletId}
    </update>

    <update id="updateFrozenAmount" parameterType="java.lang.Long">
        UPDATE uc_driver_wallet
        <set>
            frozen_amount = frozen_amount + #{amount}
        </set>
        WHERE driver_wallet_id = #{driverWalletId}
    </update>

    <update id="updateAmountByDriverId">
        UPDATE uc_driver_wallet
        <set>
            driver_wallet_amount = driver_wallet_amount + #{addAmount}
        </set>
        WHERE driver_id = #{driverId}
    </update>

    <update id="updateAbcAmountById" parameterType="java.lang.Long">
        UPDATE uc_driver_wallet
        <set>
            abc_balance = #{abcAmount}
        </set>
        WHERE driver_wallet_id = #{driverWalletId}
    </update>
    <update id="updateAbcAmountByDriverId">
        UPDATE uc_driver_wallet
        <set>
            abc_balance = abc_balance + #{amount}
        </set>
        WHERE driver_id = #{driverId}
    </update>
    <update id="updateAbcAmountByDriverIdAndAffiliatedPlatform">
        UPDATE uc_driver_wallet
        <set>
            abc_balance = abc_balance + #{amount}
        </set>
        WHERE driver_id = #{driverId} and affiliated_platform = #{affiliatedPlatform}
    </update>


    <select id="selectDriverAccount" resultType="com.lcdt.userinfo.dto.DriverAccountDto">
        select DISTINCT td.driver_id, td.driver_name, td.driver_phone,td.driver_identity_type,tw.driver_wallet_id,tw.driver_enter_remark,
        (CASE WHEN tw.driver_alipay_account is null THEN tw.driver_bank_card_no_self WHEN tw.driver_alipay_account=''
        THEN tw.driver_bank_card_no_self ELSE tw.driver_alipay_account END) as withdraw_account,
        tw.driver_wallet_amount, tw.driver_open_pay, tw.driver_ant_signed, tw.driver_enter_status,tw.abc_balance,tw.driver_open_bank_code,tw.driver_open_bank_name,tw.is_abc_bank,tw.ab_no,tw.affiliated_platform
        from tr_driver td left join uc_driver_wallet tw on td.driver_id = tw.driver_id
        left join tr_driver_auth ta on td.driver_id = ta.driver_id
        <where>
            <if test="dto.driverName!=null and dto.driverName!=''">
                and td.driver_name like concat('%',#{dto.driverName},'%')
            </if>
            <if test="dto.driverPhone!=null and dto.driverPhone!=''">
                and td.driver_phone like concat('%',#{dto.driverPhone},'%')
            </if>
            <if test="dto.driverEnterStatus==0">
                and (tw.driver_enter_status = #{dto.driverEnterStatus} or tw.driver_wallet_amount is null)
            </if>
            <if test="dto.driverEnterStatus==1 or dto.driverEnterStatus==2 or dto.driverEnterStatus==3">
                and tw.driver_enter_status = #{dto.driverEnterStatus}
            </if>
            <if test="dto.driverAntSigned==0 ">
                and (tw.driver_ant_signed = #{dto.driverAntSigned} or tw.driver_wallet_amount is null )
            </if>
            <if test="dto.driverAntSigned==1 ">
                and tw.driver_ant_signed = #{dto.driverAntSigned}
            </if>
            <if test="dto.affiliatedPlatform!=null and dto.affiliatedPlatform!=''">
                and tw.affiliated_platform = #{dto.affiliatedPlatform}
            </if>
        </where>
        order by driver_wallet_id desc
    </select>
</mapper>