package com.lcdt.userinfo.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.common.constant.RedisGroupPrefix;
import com.lcdt.common.prop.AbcProperties;
import com.lcdt.userinfo.dao.*;
import com.lcdt.userinfo.dto.AuthenticationDto;
import com.lcdt.userinfo.dto.AuthenticationQueryDto;
import com.lcdt.userinfo.dto.CompanyCertDto;
import com.lcdt.userinfo.dto.ToggleCompanyEnableDto;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.model.CompanyCertificate;
import com.lcdt.userinfo.model.CompanyQuota;
import com.lcdt.userinfo.model.SysRatesSet;
import com.lcdt.userinfo.rpc.IAuthenRpcService;
import com.lcdt.util.CheckEmptyUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @Author: yangbinq
 * @Date: 2020/2/5 10:23
 */
@Service
@Transactional
public class AuthenRpcServiceImpl implements IAuthenRpcService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private CompanyCertificateMapper companyCertificateMapper;

    @Autowired
    private UserCompRelMapper userCompRelMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private AbcProperties abcProperties;

    @Autowired
    private SysRatesSetMapper sysRatesSetMapper;

    @Autowired
    private CompanyQuotaMapper companyQuotaMapper;


    @Override
    public PageInfo authencationList(AuthenticationQueryDto dto) {
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        List<AuthenticationDto> list = userMapper.authenticationList(dto);
        LambdaQueryWrapper<SysRatesSet> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<SysRatesSet> sysRatesSets = sysRatesSetMapper.selectList(lambdaQueryWrapper);
        LambdaQueryWrapper<CompanyCertificate> companyCertificateLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<CompanyCertificate> companyCertificates = companyCertificateMapper.selectList(companyCertificateLambdaQueryWrapper);
        LambdaQueryWrapper<CompanyQuota> companyQuotaLambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<CompanyQuota> companyQuotas = companyQuotaMapper.selectList(companyQuotaLambdaQueryWrapper);
        if (list != null && !list.isEmpty()) { //查询企业帐号对应的认证信息
            for (AuthenticationDto obj : list) {
                if(CheckEmptyUtil.isNotEmpty(companyCertificates)){
                    List<CompanyCertificate> collect = companyCertificates.stream().filter(s -> s.getCompId() == (obj.getCompId())).collect(Collectors.toList());
                    obj.setCompanyCertificateList(collect);
                }
                if(CheckEmptyUtil.isNotEmpty(sysRatesSets)){
                    List<SysRatesSet> collect = sysRatesSets.stream().filter(s -> s.getRatesCompanyId() == (obj.getCompId())).collect(Collectors.toList());
                    if(CheckEmptyUtil.isNotEmpty(collect)){
                        obj.setSysRatesSet(collect.get(0));
                    }
                }
                if(CheckEmptyUtil.isNotEmpty(companyQuotas)){
                    List<CompanyQuota> collect = companyQuotas.stream().filter(s -> s.getCompId() == (obj.getCompId())).filter(s -> s.getEnable() == 1).collect(Collectors.toList());
                    if(CheckEmptyUtil.isNotEmpty(collect)){
                        obj.setCompanyQuota(collect.get(0));
                    }else {
                        List<CompanyQuota> co = companyQuotas.stream().filter(s -> s.getCompId() == (obj.getCompId())).filter(s -> s.getEnable() == 0).sorted(Comparator.comparing(CompanyQuota::getCreateDate)).collect(Collectors.toList());
                        if(CheckEmptyUtil.isNotEmpty(co)){
                            obj.setCompanyQuota(co.get(0));
                        }
                    }
                }
            }
        }
        PageInfo pg = new PageInfo(list);
        return pg;
    }

    @Override
    public IPage<CompanyCertDto> selectShipper4AccountManage(IPage<?> iPage, String fullName, Integer enterStatus) {
        IPage<CompanyCertDto> companyCertDtoIPage = companyCertificateMapper.selectShipperList4AccountMange(iPage, fullName, enterStatus);
        List<CompanyCertDto> records = companyCertDtoIPage.getRecords();
        List<CompanyCertDto> resultList = new ArrayList<>();
        records.forEach(s -> {
            String b = stringRedisTemplate.opsForValue().get(RedisGroupPrefix.COMPANY_BALANCE + s.getCompId());
            s.setAvailableAmount(b);
            resultList.add(s);
            if (CheckEmptyUtil.isNotEmpty(s.getAbNo())) {
                s.setAbNo(abcProperties.getAccountNoPre()
                        + abcProperties.getAccountNo() + s.getAbNo());
            }
        });
        companyCertDtoIPage.setRecords(resultList);
        return companyCertDtoIPage;
    }

    @Override
    public Company companyDetail(Long companyId) {
        return companyMapper.selectById(companyId);
    }

    @Override
    public List<CompanyCertificate> companyCertificateList(Long companyId) {
        return companyCertificateMapper.selectByCompanyId(companyId);
    }


    @Override
    public Company toggleEnableCompany(ToggleCompanyEnableDto dto) {
        Company company = companyMapper.selectById(dto.getCompanyId());
        company.setEnable(dto.getEnable());
        companyMapper.updateById(company);
        return company;
    }

}
