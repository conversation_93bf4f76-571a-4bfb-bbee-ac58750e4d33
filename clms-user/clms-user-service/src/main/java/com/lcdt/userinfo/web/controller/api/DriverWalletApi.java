package com.lcdt.userinfo.web.controller.api;


import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import com.lcdt.traffic.model.Driver;
import com.lcdt.userinfo.dto.DriverWithdrawalLogDto;
import com.lcdt.userinfo.model.DriverWithdrawalLog;
import com.lcdt.userinfo.rpc.DriverWithdrawalLogService;
import com.lcdt.userinfo.web.dto.PageBaseDto;
import com.lcdt.util.ResponseJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 司机钱包相关接口
 */
@RestController
@RequestMapping("/api/driverwallet")
public class DriverWalletApi {

    @Autowired
    private DriverWithdrawalLogService driverWithdrawalLogService;

    /**
     * 司机提现记录
     *
     * @param driverWithdrawalDto 查询参数
     * @return 提现记录列表
     */
    @GetMapping("/withdrawLogList")
    public JSONObject list(DriverWithdrawalLogDto driverWithdrawalDto) {
        PageInfo<DriverWithdrawalLog> withdrawalLog = driverWithdrawalLogService.selectList(driverWithdrawalDto);
        return ResponseJsonUtils.successResponseJson(new PageBaseDto(withdrawalLog.getList(), withdrawalLog.getTotal()));
    }


    /**
     * 司机提现记录总计
     *
     * @param driverWithdrawalDto 查询参数
     * @return 提现记录总计
     */
    @GetMapping("/withdrawLogTotal")
    public JSONObject withdrawLogTotal(DriverWithdrawalLogDto driverWithdrawalDto) {
        DriverWithdrawalLog withdrawalLog = driverWithdrawalLogService.selectTotal(driverWithdrawalDto);
        return ResponseJsonUtils.successResponseJson(withdrawalLog);
    }
}
