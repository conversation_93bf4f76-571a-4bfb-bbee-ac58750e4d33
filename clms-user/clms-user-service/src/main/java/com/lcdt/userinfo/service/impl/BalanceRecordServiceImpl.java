package com.lcdt.userinfo.service.impl;

import cn.hutool.core.date.DateUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.utils.IdUtils;
import com.lcdt.common.config.AliyunOssConfig;
import com.lcdt.notify.websocket.model.ExchangeMessage;
import com.lcdt.notify.websocket.model.WebNoticeTemplet;
import com.lcdt.traffic.service.NotifySendService;
import com.lcdt.userinfo.dao.BalanceRecordMapper;
import com.lcdt.userinfo.dto.BalanceRecordDto;
import com.lcdt.userinfo.model.BalanceRecord;
import com.lcdt.userinfo.model.ExportInfo;
import com.lcdt.userinfo.rpc.ExportInfoRpcService;
import com.lcdt.userinfo.service.BalanceRecordService;
import com.lcdt.util.DateUtils;
import org.springframework.stereotype.Service;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class BalanceRecordServiceImpl implements BalanceRecordService {

    @Autowired
    private BalanceRecordMapper balanceRecordMapper;

    @Autowired
    SecurityInfoGetter securityInfoGetter;

    @Autowired
    private ExportInfoRpcService exportInfoRpcService;

    @Autowired
    private AliyunOssConfig aliyunOssConfig;

    @Autowired
    private NotifySendService notifySendService;

    @Value("${isDebug}")
    private Boolean isDebug;

    @Override
    public List<BalanceRecord> getBalanceRecordList(BalanceRecordDto balanceRecordDto) {
        PageHelper.startPage(balanceRecordDto.getPageNo(), balanceRecordDto.getPageSize());
        return balanceRecordMapper.selectByCondition(balanceRecordDto);
    }


    @Override
    public int saveBalanceRecord(BalanceRecord balanceRecord) {
        return balanceRecordMapper.insert(balanceRecord);
    }


    @Override
    public BalanceRecord queryByOutTradeNo(String outTradeNo) {
        return balanceRecordMapper.selectOne(new LambdaQueryWrapper<BalanceRecord>()
                .eq(BalanceRecord::getOutTradeNo, outTradeNo));
    }

    @Override
    public void exportRecord(BalanceRecordDto dto) {
        //构建进程日志
        ExportInfo exportInfo = new ExportInfo();
        exportInfo.setCompanyId(securityInfoGetter.getCompId());
        exportInfo.setCreateId(securityInfoGetter.getUserInfo().getUserId());
        exportInfo.setCreateName(securityInfoGetter.getUserInfo().getRealName());
        exportInfo.setBeginExportDate(new Date());
        exportInfo.setExportType(1); //运单
        exportInfo.setExportStatus(0); //进行中
        Long eid = exportInfoRpcService.addExportInfo(exportInfo); //插入导入日志ID
        dto.setCompanyId(securityInfoGetter.getCompId());
        Long companyId = securityInfoGetter.getCompId();
        int count = balanceRecordMapper.selectExportCount(dto);
        if (count == 0) {
            throw new RuntimeException("没有查出来对应数据导出");
        }
        new Thread(() -> {
            //查询count
            List<BalanceRecord> list = new ArrayList<>();
            if (count > 0) {
                //计算次数
                //TODO 默认每次导出500条后续建议改成可与配置的
                int exportLimit = 500;
                Integer exportSize = count / exportLimit; //导出次数
                Integer lastExportSize = count % exportLimit;//最后一次导出的数量
                int startLimit = 0;
                int endLimit = 0;
                for (int i = 0; i < exportSize; i++) {
                    startLimit = i * exportLimit;
                    endLimit = exportLimit;
                    dto.setStartLimit(startLimit);
                    dto.setEndLimit(endLimit);
                    List<BalanceRecord> balanceRecordList = balanceRecordMapper.selectExportDataLimit(dto);
                    list.addAll(balanceRecordList);
                }
                if (lastExportSize > 0) {
                    //最后一次查询导出
                    startLimit = exportSize * exportLimit;
                    endLimit = lastExportSize;
                    dto.setStartLimit(startLimit);
                    dto.setEndLimit(endLimit);
                    List<BalanceRecord> balanceRecordList = balanceRecordMapper.selectExportDataLimit(dto);
                    list.addAll(balanceRecordList);
                }
            } else {
                throw new RuntimeException("没有查出来对应数据导出");
            }
            String fileName = "";
            if (null != list && list.size() > 0) {
                HSSFWorkbook workbook = new HSSFWorkbook();
//                String title = "运单管理" + DateUtility.date2String(new Date(), "yyyyMMdd") + IdGeneral.exportRandom6();
                String title = "余额流水" + DateUtil.format(new Date(), "yyyyMMdd") + IdUtils.exportRandom6();
                HSSFSheet sheet = workbook.createSheet(title);
                HSSFRow row = createTitle(workbook, sheet);
                int i = 1;
                for (BalanceRecord obj : list) {
                    int j = 0;
                    row = sheet.createRow(i + 0);
                    row.createCell(j).setCellValue(DateUtils.formatDate(obj.getCreateTime(), DateUtils.DATETIME_FORMAT));
                    row.createCell(++j).setCellValue(obj.getRelateOrderNo());
                    row.createCell(++j).setCellValue(obj.getSettlementNo());
                    row.createCell(++j).setCellValue(obj.getChangeType() == 0 ? "充值" : "支付运费");
                    row.createCell(++j).setCellValue(obj.getChangeType() == 0 ? obj.getChangeAmount().divide(new BigDecimal("100")).toString() : obj.getChangeAmount().divide(new BigDecimal("100")).negate().toString());
                    row.createCell(++j).setCellValue(obj.getRemark());
                    row.createCell(++j).setCellValue(obj.getOperator());
                    i++;
                }
                for (int ii = 0; ii < 11; ii++) { //列自适应
                    sheet.autoSizeColumn(ii);
                }
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

                fileName = title + ".xls";
                try {
                    ByteArrayOutputStream ba = new ByteArrayOutputStream();
                    workbook.write(ba);
                    ba.flush();
                    ba.close();
                    workbook.close();
                    ByteArrayInputStream bio = new ByteArrayInputStream(ba.toByteArray());  //将字节数组转换成输入流
                    OSS ossClient = new OSSClientBuilder().build(aliyunOssConfig.getEndpoint(),
                            aliyunOssConfig.getAccessId(),
                            aliyunOssConfig.getAccessKey());
                    String path = "balance-record-excel";
                    if (isDebug) {
                        path = "balance-record-excel_dev";
                    }
                    PutObjectResult result = ossClient.putObject(aliyunOssConfig.getBucket(), path + "/" + fileName, bio);
                    if (result != null) {
                        ExportInfo updateExportInfo = new ExportInfo();
                        updateExportInfo.setEid(eid);
                        updateExportInfo.setEndExportDate(new Date());
                        updateExportInfo.setExportStatus(2); //完成
                        updateExportInfo.setExportUrl(aliyunOssConfig.getHost() + "/" + path + "/" + fileName);
                        exportInfoRpcService.updateExportInfo(updateExportInfo);
                    }
                    ossClient.shutdown();
                    bio.close();
                    // 完成之后发送消息通知
                    ExchangeMessage exchangeMessage = new ExchangeMessage();
                    exchangeMessage.setCid(companyId.toString());
                    exchangeMessage.setBizType(2);
                    exchangeMessage.setTemplet(WebNoticeTemplet.BALANCE_RECORD_MANAGEMENT);
                    LinkedHashMap<Object, Object> map = new LinkedHashMap<>();
//                    map.put(DateUtility.getCurrDate(), "");
                    map.put(DateUtil.formatDate(new Date()), "");
                    map.put(fileName, aliyunOssConfig.getHost() + "/" + path + "/" + fileName);
                    map.put("余额流水导出", 1);
                    map.put(exportInfo.getCreateName(), exportInfo.getCreateId());
                    exchangeMessage.setParams(map);
                    notifySendService.sendWebMsg(exchangeMessage);
                } catch (Exception e) {
                    ExportInfo updateExportInfo = new ExportInfo();
                    updateExportInfo.setEid(eid);
                    updateExportInfo.setEndExportDate(new Date());
                    updateExportInfo.setExportStatus(-1); //失败
                    updateExportInfo.setRemark(e.getMessage());
                    exportInfoRpcService.updateExportInfo(updateExportInfo);
                }
            }
        }).start();
    }

    private HSSFRow createTitle(HSSFWorkbook workbook, HSSFSheet sheet) {
        HSSFRow row = sheet.createRow(0);
        //设置为居中加粗
        HSSFCellStyle style = workbook.createCellStyle();
        HSSFFont font = workbook.createFont();
        font.setBold(true);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(font);

        HSSFCell cell;
        cell = row.createCell(0);
        cell.setCellValue("记录时间");
        cell.setCellStyle(style);

        cell = row.createCell(1);
        cell.setCellValue("银行单号");
        cell.setCellStyle(style);

        cell = row.createCell(2);
        cell.setCellValue("结算单号");
        cell.setCellStyle(style);

        cell = row.createCell(3);
        cell.setCellValue("变动类型");
        cell.setCellStyle(style);

        cell = row.createCell(4);
        cell.setCellValue("金额");
        cell.setCellStyle(style);

        cell = row.createCell(5);
        cell.setCellValue("备注");
        cell.setCellStyle(style);

        cell = row.createCell(6);
        cell.setCellValue("操作人");
        cell.setCellStyle(style);
        return row;
    }
}
