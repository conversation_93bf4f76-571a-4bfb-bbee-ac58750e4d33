package com.lcdt.userinfo.service.impl;

import com.lcdt.common.constant.RedisGroupPrefix;
import com.lcdt.traffic.vo.ConstantVO;
import com.lcdt.userinfo.dao.AbAccountBookMapper;
import com.lcdt.userinfo.model.AbAccountBook;
import com.lcdt.userinfo.service.AbAccountBookService;
import com.lcdt.util.CheckEmptyUtil;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2022/3/11 17:49
 */
@Service
public class AbAccountBookServiceImpl implements AbAccountBookService {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AbAccountBookMapper abAccountBookMapper;

    @Value("${isDebug}")
    private Boolean isDebug;


    @Override
    public String generateAbNo() {
        //1.从redis里面拿取数据并且步长+1
        String s = redisTemplate.opsForValue().get(RedisGroupPrefix.ABC_NO);
        if (CheckEmptyUtil.isEmpty(s)) {
            boolean tryLock = false;
            RLock lock = redissonClient.getLock(RedisGroupPrefix.ABC_NO_LOCK);
            try {
                tryLock = lock.tryLock(0, 30, TimeUnit.SECONDS);
                String maxAbNo = abAccountBookMapper.selectMaxAbNo();
                //如果为空，那么代表着可能是第一次进入，此时设置从1开始
                if (CheckEmptyUtil.isEmpty(maxAbNo)) {
                    lock.unlock();
                    if (isDebug) {
                        redisTemplate.opsForValue().set(RedisGroupPrefix.ABC_NO, String.valueOf(10));
                    }
                    return getGenerateSerialNumberByCode();
                } else {
                    //设置最大值并且步长+10赋值到redis中
                    int max = Integer.valueOf(maxAbNo);
                    max += 10;
                    redisTemplate.opsForValue().set(RedisGroupPrefix.ABC_NO, String.valueOf(max));
                    lock.unlock();
                    return getGenerateSerialNumberByCode();
                }
            } catch (Exception ex) {
                lock.unlock();
                throw new RuntimeException("生成农行账簿号失败，redis加锁，请再次重试!");
            }
        } else {
            return getGenerateSerialNumberByCode();
        }
    }

    @Override
    public int insertAccountBook(AbAccountBook abAccountBook) {
        return abAccountBookMapper.insert(abAccountBook);
    }


    private String getGenerateSerialNumberByCode() {
        String suffixCode = RedisGroupPrefix.ABC_NO;
        Long id = redisTemplate.opsForValue().increment(suffixCode, 1L);   //1
        if (isDebug) {
            String idStr = org.apache.commons.lang3.StringUtils.leftPad(String.valueOf(id), ConstantVO.PADDING_LEFT_SIZE_TEN, ConstantVO.PADDING_STR);
            return idStr;
        } else {
            String idStr = org.apache.commons.lang3.StringUtils.leftPad(String.valueOf(id), ConstantVO.PADDING_LEFT_SIZE_NINE, ConstantVO.PADDING_STR);
            return String.format(ConstantVO.SERIAL_NUMBER_TEMPLATE, ConstantVO.FLAG_1, idStr);
        }
    }
}
