package com.lcdt.userinfo.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.userinfo.dao.CompanyMapper;
import com.lcdt.userinfo.dto.CompanyQueryDto;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.service.CompanyManagerService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: lyqishan
 * @date: 2019-04-24 11:05
 * @description:
 */
@Transactional
@Service
public class CompanyManagerServiceImpl implements CompanyManagerService {

    @Autowired
    private CompanyMapper companyMapper;

    @Override
    public PageInfo queryCompanyListByCompanyDto(CompanyQueryDto companyQueryDto) {
        PageHelper.startPage(companyQueryDto.getPageNo(),companyQueryDto.getPageSize());
        return new PageInfo(companyMapper.selectByCompanyDto(companyQueryDto));
    }

    @Override
    public List<Company> selectByCompanyIds(List<Long> companyIds){
        return companyMapper.selectByCompanyIds(companyIds);
    }
}
