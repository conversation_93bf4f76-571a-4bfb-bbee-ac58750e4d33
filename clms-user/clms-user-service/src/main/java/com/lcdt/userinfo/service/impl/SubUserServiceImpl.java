package com.lcdt.userinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.lcdt.security.exception.CustomException;
import com.lcdt.security.exception.GerenicRunException;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.utils.SecurityUtils;
import com.lcdt.notify.rpcservice.ValidCodeService;
import com.lcdt.userinfo.dao.RoleUserMapper;
import com.lcdt.userinfo.dao.SubUserMapper;
import com.lcdt.userinfo.dao.UserCompRelMapper;
import com.lcdt.userinfo.dao.UserGroupMapper;
import com.lcdt.userinfo.dto.SubUserDto;
import com.lcdt.userinfo.dto.UserCompDto;
import com.lcdt.userinfo.dto.UserDto;
import com.lcdt.userinfo.model.*;
import com.lcdt.userinfo.service.SubUserService;
import com.lcdt.userinfo.service.UserService;
import com.lcdt.util.CheckEmptyUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SubUserServiceImpl implements SubUserService {

    @Autowired
    private UserService userService;


    @Autowired
    private SubUserMapper subUserMapper;
    @Autowired
    private RoleUserMapper roleUserMapper;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private ValidCodeService validCodeService;

    @Autowired
    private UserCompRelMapper userCompRelMapper;

    @Autowired
    private UserGroupMapper userGroupMapper;


    @Override
    public List<UserDto> selectByCondition(UserDto userDto) {
        userDto.setCompanyId(securityInfoGetter.getCompId());
        PageHelper.startPage(userDto.getPageNo(), userDto.getPageSize());
        return subUserMapper.selectByCondition(userDto);
    }

    @Override
    public int addSubUser(SubUserDto subUserDto) {
        //查看是否手机号重复
        UserDto users = subUserMapper.selectUserByPhone(subUserDto.getPhone());
        if(CheckEmptyUtil.isNotEmpty(users)){
            throw new GerenicRunException("手机号重复，请确认");
        }
        // 1.插入user表数据 2.插入用户企业关系表 3.插入角色用户关系
        User user = new User();
        user.setMobileCountry("86");
        user.setRealName(subUserDto.getName());
        user.setPwd(SecurityUtils.encryptPassword(subUserDto.getPwd()));
        user.setPhone(subUserDto.getPhone());
        user.setIsSub(Integer.parseInt("0"));
        user.setUserStatus(Short.parseShort("1"));
        Integer userType = securityInfoGetter.getUserInfo().getUserType();
        user.setUserType(userType);
        user.setCompanyId(securityInfoGetter.getCompId());
        int rows = subUserMapper.insert(user);
        // 构建企业关系表信息并插入
        UserCompRel rel = securityInfoGetter.getCompInfo();
        rel.setUserId(user.getUserId());
        rel.setIsCreate(Short.parseShort("2"));
        rel.setEnable(Boolean.FALSE);
        rel.setCreateDate(new Date());
        rel.setName(user.getRealName());
        rows += userCompRelMapper.insert(rel);

        if (rows > 0 && !ObjectUtils.isEmpty(subUserDto.getRoleIds())) {
            RoleUser roleUser = new RoleUser();
            roleUser.setUserId(user.getUserId());
            roleUser.setCompanyId(securityInfoGetter.getCompId());
            rows += roleUserMapper.insertBatch(subUserDto.getRoleIds(), roleUser);
        }

        //用户组信息
        if (rows > 0 && subUserDto.getGroupIds() != null && subUserDto.getGroupIds().length > 0) {
            List<UserGroup> userGroups = new ArrayList<>();
            for (int i = 0; i < subUserDto.getGroupIds().length; i++) {
                UserGroup userGroup = new UserGroup();
                userGroup.setCompanyId(securityInfoGetter.getCompId());
                userGroup.setUserId(user.getUserId());
                userGroup.setGroupId(subUserDto.getGroupIds()[i]);
                userGroups.add(userGroup);
            }
            rows += userGroupMapper.insertBatch(userGroups);
        }

        return rows;
    }

    @Override
    public int editSubUser(SubUserDto subUserDto) {
        // 1.更新子账号内容 2.更新角色关系
        User user = new User();
        user.setUserId(subUserDto.getUserId());
        user.setRealName(subUserDto.getName());
        int rows = subUserMapper.updateById(user);
        if (rows > 0 && subUserDto.getRoleIds().length > 0) {
            // 1.删除原来的关联关系 2.重新这只角色用户关系
            roleUserMapper.delete(new QueryWrapper<RoleUser>().lambda().eq(RoleUser::getUserId, subUserDto.getUserId())
                    .eq(RoleUser::getCompanyId, securityInfoGetter.getCompId()));
            RoleUser roleUser = new RoleUser();
            roleUser.setUserId(subUserDto.getUserId());
            roleUser.setCompanyId(securityInfoGetter.getCompId());
            rows += roleUserMapper.insertBatch(subUserDto.getRoleIds(), roleUser);
        }

        //用户组信息
        if (rows > 0 && subUserDto.getGroupIds() != null) {
            //先删除原有的项目组
            QueryWrapper<UserGroup> qw = new QueryWrapper<>();
            qw.eq("user_id", subUserDto.getUserId());
            qw.eq("company_id", securityInfoGetter.getCompId());
            userGroupMapper.delete(qw);
            if (subUserDto.getGroupIds().length > 0) {
                List<UserGroup> userGroups = new ArrayList<>();
                for (int i = 0; i < subUserDto.getGroupIds().length; i++) {
                    UserGroup userGroup = new UserGroup();
                    userGroup.setCompanyId(securityInfoGetter.getCompId());
                    userGroup.setUserId(user.getUserId());
                    userGroup.setGroupId(subUserDto.getGroupIds()[i]);
                    userGroups.add(userGroup);
                }
                rows += userGroupMapper.insertBatch(userGroups);
            }
        }

        return rows;
    }

    @Override
    public int delSubUser(Long userId) {
        int rows = 0;
        /**
         * 1.必须是被禁用的子账号才可以删除
         * 2.先删除子账号主信息
         * 3.删除子账号管理角色信息
         * 4.删除子账户组信息
         */
        User user = userService.queryByUserId(userId);
        if (ObjectUtils.isEmpty(user)) {
            throw new CustomException("发生异常");
        }
        if (user.getUserStatus() == 1) {
            throw new CustomException("子账号状态没有禁用，无法删除");
        }
        if (user.getUserType() == 2) {
            throw new CustomException("管理员账户无法删除");
        }
        rows += userService.deleteByUserIdAndCompanyId(userId, securityInfoGetter.getCompId());
        if (rows > 0) {
            roleUserMapper.delete(new QueryWrapper<RoleUser>().lambda().eq(RoleUser::getUserId, userId)
                    .eq(RoleUser::getCompanyId, securityInfoGetter.getCompId()));
            userGroupMapper.delete(new QueryWrapper<UserGroup>().lambda().eq(UserGroup::getUserId, userId)
                    .eq(UserGroup::getCompanyId, securityInfoGetter.getCompId()));
        }
        return rows;
    }

    @Override
    public int setStatus(User user) {
        return subUserMapper.update(user, new UpdateWrapper<User>().lambda().eq(User::getUserId, user.getUserId()));
    }

    @Override
    public List<String> getPermsInfo(String phone, Long companyId) {
        return subUserMapper.selectPermsByUserAndCompanyId(phone, companyId);
    }

    @Override
    public List<Menu> getUserPerms(Long userId, Long companyId) {
        return subUserMapper.selectUserPerms(userId, companyId);
    }

    @Override
    public UserCompDto getUserByPhoneAndCompId(String phone, Long companyId) {
        return subUserMapper.selectUserByPhoneAndCompanyId(phone, companyId);
    }


}
