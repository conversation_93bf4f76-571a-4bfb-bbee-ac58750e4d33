package com.lcdt.userinfo.rpc.impl;

import com.github.pagehelper.PageInfo;
import com.lcdt.userinfo.dto.FeedbackDto;
import com.lcdt.userinfo.dto.FeedbackParamsDto;
import com.lcdt.userinfo.model.Feedback;
import com.lcdt.userinfo.rpc.FeedbackRpcService;
import com.lcdt.userinfo.service.FeedbackService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class FeedbackRpcServiceImpl implements FeedbackRpcService {
    @Autowired
    private FeedbackService feedbackService;
    @Override
    public PageInfo<Feedback> queryFeedbackManageList(FeedbackParamsDto feedbackParamsDto) {

        return feedbackService.feedbackList(feedbackParamsDto);
    }

    @Override
    public int sbReply(FeedbackDto feedbackDto, Long userId) {
        return feedbackService.updateFb(feedbackDto,userId);
    }
}
