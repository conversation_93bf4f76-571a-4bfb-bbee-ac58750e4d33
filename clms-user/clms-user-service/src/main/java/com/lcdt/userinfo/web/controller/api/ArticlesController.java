package com.lcdt.userinfo.web.controller.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lcdt.userinfo.model.Articles;
import com.lcdt.userinfo.service.ArticlesService;
import com.lcdt.userinfo.utils.JSONResponseUtil;
import com.lcdt.userinfo.utils.ResponseMessage;
import com.lcdt.userinfo.web.dto.PageBaseDto;
import com.lcdt.userinfo.web.dto.PageResultDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 文章管理控制器
 */
/**
 * 文章管理控制器
 */
@RestController
@RequestMapping("/articles")
public class ArticlesController {

    @Autowired
    private ArticlesService articlesService;

    /**
     * 分页查询文章列表
     *
     * @param pageNum  当前页码，默认为1
     * @param pageSize 每页条数，默认为10
     * @param title    文章标题（模糊查询）
     * @param type     文章类型
     * @return 分页结果
     */
    /**
     * 分页查询文章列表
     *
     * @param pageNum  当前页码，默认为1
     * @param pageSize 每页条数，默认为10
     * @param title    文章标题（模糊查询）
     * @param type     文章类型
     * @return 分页结果
     */
    @GetMapping("/list")
    public PageBaseDto<Articles> list(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) Integer type) {
        IPage<Articles> page = articlesService.queryArticlePage(pageNum, pageSize, title, type);
        return new PageBaseDto<>(page.getRecords(), page.getTotal());
    }

    /**
     * 获取文章详情
     *
     * @param id 文章ID
     * @return 文章详情
     */
    /**
     * 获取文章详情
     *
     * @param id 文章ID
     * @return 文章详情
     */
    @GetMapping("/detail")
    public ResponseMessage<Articles> detail(Long id) {
        Articles article = articlesService.getArticleDetail(id);
        return JSONResponseUtil.success(article);
    }

    /**
     * 新增文章
     *
     * @param article 文章信息
     * @return 是否成功
     */
    /**
     * 新增文章
     *
     * @param article 文章信息
     * @return 是否成功
     */
    @PostMapping("/add")
    public ResponseMessage<Boolean> add(@Valid @RequestBody Articles article) {
        boolean result = articlesService.addArticle(article);
        return result ? JSONResponseUtil.success(true) : JSONResponseUtil.failure("新增文章失败", -1);
    }

    /**
     * 更新文章
     * 文章id必传
     *
     * @param article 文章信息
     * @return 是否成功
     */
    /**
     * 更新文章
     * 文章id必传
     *
     * @param article 文章信息
     * @return 是否成功
     */
    @PostMapping("/update")
    public ResponseMessage<Boolean> update(@Valid @RequestBody Articles article) {
        boolean result = articlesService.updateArticle(article);
        return result ? JSONResponseUtil.success(true) : JSONResponseUtil.failure("更新文章失败", -1);
    }

    /**
     * 删除文章
     *
     * @param id 文章ID
     * @return 是否成功
     */
    /**
     * 删除文章
     *
     * @param id 文章ID
     * @return 是否成功
     */
    @PostMapping("/delete")
    public ResponseMessage<Boolean> delete(Long id) {
        boolean result = articlesService.deleteArticle(id);
        return result ? JSONResponseUtil.success(true) : JSONResponseUtil.failure("删除文章失败", -1);
    }
}
