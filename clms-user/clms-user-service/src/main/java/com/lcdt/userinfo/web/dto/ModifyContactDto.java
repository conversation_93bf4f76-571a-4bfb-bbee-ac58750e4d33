package com.lcdt.userinfo.web.dto;


import org.hibernate.validator.constraints.Email;
import org.hibernate.validator.constraints.NotBlank;

import jakarta.validation.constraints.Size;

/**
 * Created by ss on 2017/11/6.
 */
public class ModifyContactDto {

	/** 联系人姓名 */
	@NotBlank
	@Size(min = 1,max = 10)
	private String contactName;

	/** 职位 */
	private String contactDuty;

	/** 联系人手机 */
	@NotBlank
	@Size(max = 30)
	private String contacTel;
	@Email
	private String contactEmail;

	@Size(max = 150)
	private String contactRemark;



	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactDuty() {
		return contactDuty;
	}

	public void setContactDuty(String contactDuty) {
		this.contactDuty = contactDuty;
	}

	public String getContacTel() {
		return contacTel;
	}

	public void setContacTel(String contacTel) {
		this.contacTel = contacTel;
	}

	public String getContactEmail() {
		return contactEmail;
	}

	public void setContactEmail(String contactEmail) {
		this.contactEmail = contactEmail;
	}

	public String getContactRemark() {
		return contactRemark;
	}

	public void setContactRemark(String contactRemark) {
		this.contactRemark = contactRemark;
	}
}