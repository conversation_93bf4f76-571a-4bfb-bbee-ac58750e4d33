package com.lcdt.userinfo.service.impl;

/**
 * Created by ss on 2017/11/14.
 */
//@Service
//public class EmployeeServiceImpl implements ApplicationEventPublisherAware {
public class EmployeeServiceImpl {

    /*@Autowired
    UserService userService;

    @Autowired
    UserCompRelMapper userCompanyDao;

    @Autowired
    UserRoleService roleService;

    @Autowired
    DepartmentService departmentService;

    @Autowired
    LoginLogService loginLogService;

    ApplicationEventPublisher applicationEventPublisher;

    @Transactional(rollbackFor = Exception.class)
    public boolean removeUserCompRel(Long userCompId) {
        UserCompRel userCompRel = userCompanyDao.selectByPrimaryKey(userCompId);
        if (userCompRel != null) {
            if (userCompRel.getIsCreate() == 1) {
                return false;
            }
            userCompanyDao.deleteByPrimaryKey(userCompRel.getUserCompRelId());
            roleService.removeUserRole(userCompRel.getUserId(), userCompRel.getCompId());
//            groupService.deleteUserGroupRelation(userCompRel.getUserId(), userCompRel.getCompId());
            return true;
        }
        return false;
    }


    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> addEmployee(CreateEmployeeAccountDto dto, Long companyId) throws PhoneHasRegisterException {
        Map<String, Object> map = new HashMap<>();
        String phone = dto.getUserPhoneNum();
        boolean phoneBeenRegister = userService.isPhoneBeenRegister(phone);
        User user = null;
        if (!phoneBeenRegister) {
            //注册
            user = userService.registerUser(dto);
        } else {
            user = userService.selectUserByPhone(phone);
        }
        //创建公司关联
        //是否已加入公司
        boolean userInCompany = isUserInCompany(user.getUserId(), companyId);
        if (userInCompany) {
            map.put("result", false);
            return map;
        }
        String department = departmentService.getIdsNames(dto.getDepartIds());
        UserCompRel userCompRel = new UserCompRel();
        userCompRel.setCompId(companyId);
        userCompRel.setUserId(user.getUserId());
        userCompRel.setCreateDate(new Date());
        userCompRel.setDeptIds(dto.getDepartIds());
        userCompRel.setDeptNames(department);
        userCompRel.setDuty(dto.getDuty());
        userCompRel.setComment(dto.getComment());
        userCompRel.setEmail(dto.getEmail());
        userCompRel.setNickName(dto.getNickName());
        userCompRel.setName(dto.getName());
        userCompanyDao.insert(userCompRel);
        if (dto.getRoles() != null && !dto.getRoles().isEmpty()) {
            roleService.setCompanyUserRole(user.getUserId(), companyId, dto.getRoles());
        }

        if (dto.getGroups() != null && !dto.getGroups().isEmpty()) {
//            groupService.setCompanyUserGroup(user.getUserId(), companyId, dto.getGroups());
        }

        final AddEmployeeEvent addEmployeeEvent = new AddEmployeeEvent(userCompRel);
        addEmployeeEvent.setNewUser(!phoneBeenRegister);
        addEmployeeEvent.setInitPwd(dto.getPassword());
        applicationEventPublisher.publishEvent(addEmployeeEvent);
        map.put("result", true);
        map.put("user", user);
        return map;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<UserCompRel> queryAllEmployee(SearchEmployeeDto search) {
        List<UserCompRel> userCompRels = userCompanyDao.selectBySearchDto(search);
        ArrayList<EmployeeDto> employeeDtos = new ArrayList<>(userCompRels.size());
//		for (UserCompRel compRel : userCompRels) {
//			EmployeeDto employeeDto = new EmployeeDto();
//			Long userId = compRel.getUser().getUserId();
//			List<Group> groups = groupService.userCompanyGroups(userId,compRel.getCompId());
//			compRel.setGroups(groups);
//			List<Role> roles = roleService.userCompanyRole(userId, compRel.getCompId());
//			compRel.setRoles(roles);
//			//获取员工所在部门
//			if(compRel.getDeptIds()!=null && !StringUtils.isEmpty(compRel.getDeptIds()))
//			{
//				Department department = departmentService.getDepartment(Long.parseLong(compRel.getDeptIds()));
//				compRel.setDeptNames(department.getDeptName());
//			}
//			employeeDtos.add(employeeDto);
//			LoginLog loginLog = loginLogService.userLastLogin(userId, compRel.getCompId());
//			compRel.setLoginLog(loginLog);
//		}
        return userCompRels;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean isUserInCompany(Long userId, Long companyId) {
        List<UserCompRel> userCompRels = userCompanyDao.selectByUserIdCompanyId(userId, companyId);
        return userCompRels != null && !userCompRels.isEmpty();
    }

    @Transactional(rollbackFor = Exception.class)
    public UserCompRel updateEmployee(UpdateEmployeeAccountDto dto) {
        Long userCompRelId = dto.getUserCompRelId();
        UserCompRel userCompRel = userCompanyDao.selectByPrimaryKey(userCompRelId);
        User user = userCompRel.getUser();

//		BeanUtils.copyProperties(dto, user);
//		userService.updateUser(user);
        //更新用户部门信息

        userCompRel.setComment(dto.getComment());
        userCompRel.setDuty(dto.getDuty());
        userCompRel.setEmail(dto.getEmail());
        userCompRel.setNickName(dto.getNickName());
        userCompRel.setName(dto.getName());
        String departIds = dto.getDepartIds();

        if (!StringUtils.isEmpty(departIds)) {
            String idsNames = departmentService.getIdsNames(departIds);
            userCompRel.setDeptNames(idsNames);
            userCompRel.setDeptIds(departIds);
        }

        userCompanyDao.updateByPrimaryKey(userCompRel);

        //更新权限
        if (dto.getRoles() != null && !dto.getRoles().isEmpty()) {
            roleService.updateCompanyUserRole(user.getUserId(), userCompRel.getCompany().getCompId(), dto.getRoles());
        }
        //更新用户组
        if (dto.getGroups() != null && !dto.getGroups().isEmpty()) {
//            groupService.updateCompanyUsergroup(user.getUserId(), userCompRel.getCompany().getCompId(), dto.getGroups());
        }
        userCompRel = userCompanyDao.selectByPrimaryKey(userCompRelId);
        return userCompRel;
    }


    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public UserCompRel toggleEnableEmployee(ToggleCompanyEnableDto dto) {
*//*        UserCompRel userCompRel = userCompanyDao.selectByPrimaryKey(dto.getUserCompRelId());
        if (userCompRel.getIsCreate() == 1) {
            throw new RuntimeException("管理员无法禁用");
        }

        userCompRel.setEnable(dto.getEnable());
        userCompanyDao.updateByPrimaryKey(userCompRel);
        return userCompRel;*//*
        return null;
    }

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> editUserRole(EditUserRoleDto dto, Long companyId) {
        Map<String, Object> map = new HashMap<>();
        User user = null;
        if (dto.getUserId() != null) {
            user = userService.queryByUserId(dto.getUserId());
        }
        if (user != null) {

            //查询该员工是否设置了角色组和业务组
            List<Role> roles = roleService.userCompanyRole(user.getUserId(), companyId);
            if (roles != null && roles.size() > 0) {
                //更新权限
                if (dto.getRoles() != null && !dto.getRoles().isEmpty()) {
                    roleService.updateCompanyUserRole(user.getUserId(), companyId, dto.getRoles());
                }
            } else {
                if (dto.getRoles() != null && !dto.getRoles().isEmpty()) {
                    roleService.setCompanyUserRole(user.getUserId(), companyId, dto.getRoles());
                }
            }
            *//*List<Group> groups = groupService.userCompanyGroups(user.getUserId(), companyId);
            if (groups != null && groups.size() > 0) {
                //更新用户组
                if (dto.getGroups() != null && !dto.getGroups().isEmpty()) {
                    groupService.updateCompanyUsergroup(user.getUserId(), companyId, dto.getGroups());
                }
            } else {
                if (dto.getGroups() != null && !dto.getGroups().isEmpty()) {
                    groupService.setCompanyUserGroup(user.getUserId(), companyId, dto.getGroups());
                }
            }*//*
            map.put("result", true);
        } else {
            map.put("result", false);
        }
        return map;
    }

    public List<UserCompRel> queryEmployeeStat(SearchEmployeeDto dto) {
        List<UserCompRel> userCompRels = new ArrayList<>();
        UserCompRel compRel = new UserCompRel();
//        List<Group> groups = groupService.userCompanyGroups(dto.getUserId(), dto.getCompanyId());
//        compRel.setGroups(groups);
        List<Role> roles = roleService.userCompanyRole(dto.getUserId(), dto.getCompanyId());
        compRel.setRoles(roles);
        LoginLog loginLog = loginLogService.userLastLogin(dto.getUserId(), dto.getCompanyId());
        compRel.setLoginLog(loginLog);
        userCompRels.add(compRel);
        return userCompRels;
    }*/
}
