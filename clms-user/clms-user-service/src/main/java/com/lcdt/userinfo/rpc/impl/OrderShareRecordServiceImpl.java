package com.lcdt.userinfo.rpc.impl;

import com.lcdt.userinfo.dao.OrderShareRecordMapper;
import com.lcdt.userinfo.model.OrderShareRecord;
import com.lcdt.userinfo.rpc.OrderShareRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021-08-16
 */
@Service
public class OrderShareRecordServiceImpl implements OrderShareRecordService {

    @Autowired
    private OrderShareRecordMapper orderShareRecordMapper;

    @Override
    public int save(OrderShareRecord orderShareRecord) {
        return orderShareRecordMapper.insert(orderShareRecord);
    }
}
