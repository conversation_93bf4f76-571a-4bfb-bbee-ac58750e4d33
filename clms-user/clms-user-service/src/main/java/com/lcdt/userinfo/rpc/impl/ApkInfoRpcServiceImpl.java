package com.lcdt.userinfo.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.lcdt.common.config.AliyunOssConfig;
import com.lcdt.userinfo.dao.ApkInfoMapper;
import com.lcdt.userinfo.model.ApkInfo;
import com.lcdt.userinfo.rpc.ApkInfoRpcService;
import com.lcdt.util.CheckEmptyUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @author: lyqishan
 * @date: 2020/7/18 14:20
 * @description:
 */
@Service
@Transactional
public class ApkInfoRpcServiceImpl implements ApkInfoRpcService {

    @Autowired
    private ApkInfoMapper apkInfoMapper;


    @Autowired
    private AliyunOssConfig aliyunOssConfig;

    @Override
    public ApkInfo queryLastApkInfo(String system) {
        return apkInfoMapper.selectLastOne(system);
    }

    @Override
    public List<ApkInfo> selectList(ApkInfo dto) {
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        List<ApkInfo> apkInfos = apkInfoMapper.selectByCondition(dto);
        return apkInfos;
    }

    @Override
    public int insert(ApkInfo dto) {
        dto.setUpdateStatus(0);
        Integer i = apkInfoMapper.selectMaxVersionCode(dto.getSourceSystem());
        if(CheckEmptyUtil.isEmpty(i)){
            dto.setVersionCode(1);
        }else {
            dto.setVersionCode(++i);
        }
        return apkInfoMapper.insert(dto);
    }

    @Override
    public int update(ApkInfo dto) {
        LambdaUpdateWrapper<ApkInfo> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ApkInfo::getApkId,dto.getApkId());
        int update = apkInfoMapper.update(dto, lambdaUpdateWrapper);
        return update;
    }


    @Override
    public String downLoad(Long apkId) throws Exception {
        LambdaQueryWrapper<ApkInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ApkInfo::getApkId,apkId);
        List<ApkInfo> apkInfos = apkInfoMapper.selectList(lambdaQueryWrapper);
        if(CheckEmptyUtil.isEmpty(apkInfos)){
            throw new  RuntimeException("根据id查询不到对应的apk记录");
        }
        //根据url读取oss，并输出文件
        ApkInfo apkInfo = apkInfos.get(0);
        String downloadUrl = apkInfo.getDownloadUrl();
        return downloadUrl;
    }

    @Override
    public int publish(Long apkId, String  userName) {
        LambdaQueryWrapper<ApkInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ApkInfo::getApkId,apkId);
        List<ApkInfo> apkInfos = apkInfoMapper.selectList(lambdaQueryWrapper);
        if(CheckEmptyUtil.isEmpty(apkInfos)){
            throw new  RuntimeException("根据id查询不到对应的apk记录");
        }
        ApkInfo apkInfo = apkInfos.get(0);
        apkInfo.setUpdater(userName);
        apkInfo.setUpdateDate(new Date());
        apkInfo.setPublishDate(new Date());
        apkInfo.setUpdateStatus(1);
        LambdaUpdateWrapper<ApkInfo> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ApkInfo::getApkId,apkId);
        int update = apkInfoMapper.update(apkInfo, lambdaUpdateWrapper);
        return update;
    }

    @Override
    public int end(Long apkId, String  userNamer) {
        LambdaQueryWrapper<ApkInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ApkInfo::getApkId,apkId);
        List<ApkInfo> apkInfos = apkInfoMapper.selectList(lambdaQueryWrapper);
        if(CheckEmptyUtil.isEmpty(apkInfos)){
            throw new  RuntimeException("根据id查询不到对应的apk记录");
        }
        ApkInfo apkInfo = apkInfos.get(0);
        apkInfo.setUpdater(userNamer);
        apkInfo.setUpdateDate(new Date());
        apkInfo.setUpdateStatus(0);
        LambdaUpdateWrapper<ApkInfo> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(ApkInfo::getApkId,apkId);
        lambdaUpdateWrapper.set(ApkInfo::getPublishDate,null);
        int update = apkInfoMapper.update(apkInfo, lambdaUpdateWrapper);
        return update;
    }


}
