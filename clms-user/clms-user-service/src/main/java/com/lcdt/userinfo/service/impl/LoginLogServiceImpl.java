package com.lcdt.userinfo.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.userinfo.dao.LoginLogMapper;
import com.lcdt.userinfo.model.LoginLog;
import com.lcdt.userinfo.model.LoginLogDto;
import com.lcdt.userinfo.service.LoginLogService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Service
@Transactional
public class LoginLogServiceImpl implements LoginLogService{

    @Autowired
    LoginLogMapper loginLogMapper;

    @Override
    public void saveLog(LoginLog log){
        loginLogMapper.insert(log);
    }

    @Override
    public LoginLog userLastLogin(Long userId, Long companyId) {
        List<LoginLogDto> loginLogDtos = loginLogMapper.selectByCompanyId(companyId, null, userId,null,null);
        if (CollectionUtils.isEmpty(loginLogDtos)) {
            return null;
        }else{
            return loginLogDtos.get(0);
        }
    }

    @Override
    public PageInfo loginLogList(Long companyId,  String username , Long userId,  Date beginTime,  Date endTime,Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        return new PageInfo(loginLogMapper.selectByCompanyId(companyId,username,userId,beginTime,endTime));
    }


}
