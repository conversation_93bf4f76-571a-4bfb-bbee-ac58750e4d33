package com.lcdt.userinfo.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lcdt.common.constant.RedisGroupPrefix;
import com.lcdt.userinfo.dao.CompanyMapper;
import com.lcdt.userinfo.dao.SysRatesSetMapper;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.model.SysRatesSet;
import com.lcdt.userinfo.rpc.SysRatesSetService;
import com.lcdt.util.CheckEmptyUtil;
import com.lcdt.util.JsonMapper;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: xuerr
 * @date: 2020-08-27
 * @description:
 */
@Service
@Transactional
public class SysRatesSetRpcServiceImpl implements SysRatesSetService {

    @Autowired
    SysRatesSetMapper sysRatesSetMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private CompanyMapper companyMapper;

    @PostConstruct
    public void initParam() {
        // 查询出所有数据
        QueryWrapper<SysRatesSet> queryWrapper = new QueryWrapper<>();
        List<SysRatesSet> list = sysRatesSetMapper.selectList(queryWrapper);
        list.forEach(s -> {
            //TODO 第一次执行的时候保留，后面发版干掉
            redisTemplate.delete(RedisGroupPrefix.SYSRATES_SUFFIX + s.getRatesCompanyId().toString());
            //将数据放入到组里面
            redisTemplate.opsForValue().set(RedisGroupPrefix.SYSRATES_SUFFIX + s.getRatesCompanyId().toString(), JsonMapper.toJsonString(s));
        });
    }

    @Override
    public int insertSysRatesSet(Company company) {
        QueryWrapper<SysRatesSet> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rates_company_id", company.getCompId());
        List<SysRatesSet> list = sysRatesSetMapper.selectList(queryWrapper);
        if (list != null && list.size() > 0) {
            return 0;
        } else {
            SysRatesSet sysRatesSet = new SysRatesSet();
            sysRatesSet.setRatesFrist(new BigDecimal(0));
            sysRatesSet.setRatesSecond(new BigDecimal(0));
            sysRatesSet.setRatesType(2);
            sysRatesSet.setRatesCompanyId(company.getCompId());
            sysRatesSet.setRatesCompanyName(company.getFullName());
            sysRatesSet.setDirectPlanFlag(1);
            sysRatesSet.setDirectPlanVehiclePrice(1);
            sysRatesSet.setDriectPlanAmountType(1);
            sysRatesSet.setFixedPlanFlag(1);
            sysRatesSet.setFixedPlanVehiclePrice(1);
            sysRatesSet.setFixedPlanAmountType(1);
            int i = sysRatesSetMapper.insert(sysRatesSet);
            //新增的数据放入到redis里面去
            redisTemplate.opsForValue().set(RedisGroupPrefix.SYSRATES_SUFFIX + sysRatesSet.getRatesCompanyId().toString(), JsonMapper.toJsonString(sysRatesSet));
            return i;
        }
    }

    @Override
    public SysRatesSet queryRatesByCompanyId(Long companyId) {
        QueryWrapper<SysRatesSet> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rates_company_id", companyId);
        List<SysRatesSet> list = sysRatesSetMapper.selectList(queryWrapper);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public int insertFreighteMode(SysRatesSet sysRatesSet) {
        QueryWrapper<SysRatesSet> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rates_company_id", sysRatesSet.getRatesCompanyId());
        List<SysRatesSet> list = sysRatesSetMapper.selectList(queryWrapper);
        if (CheckEmptyUtil.isNotEmpty(list)) {
            SysRatesSet param = list.get(0);
            LambdaUpdateWrapper<SysRatesSet> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(SysRatesSet::getRatesId, param.getRatesId());
            SysRatesSet result = new SysRatesSet();
            result.setDirectPlanFlag(sysRatesSet.getDirectPlanFlag());
            result.setDirectPlanVehiclePrice(sysRatesSet.getDirectPlanVehiclePrice());
            result.setDriectPlanAmountType(sysRatesSet.getDriectPlanAmountType());
            result.setFixedPlanFlag(sysRatesSet.getFixedPlanFlag());
            result.setFixedPlanVehiclePrice(sysRatesSet.getFixedPlanVehiclePrice());
            result.setFixedPlanAmountType(sysRatesSet.getFixedPlanAmountType());
            result.setUpdateTime(new Date());
            Company company = companyMapper.selectById(sysRatesSet.getRatesCompanyId());
            if (CheckEmptyUtil.isNotEmpty(company)) {
                result.setRatesCompanyName(company.getFullName());
            }
            int update = sysRatesSetMapper.update(result, lambdaUpdateWrapper);
            List<SysRatesSet> o = sysRatesSetMapper.selectList(queryWrapper);
            SysRatesSet sysRatesSet1 = o.get(0);
            redisTemplate.opsForValue().set(RedisGroupPrefix.SYSRATES_SUFFIX + sysRatesSet1.getRatesCompanyId().toString(), JsonMapper.toJsonString(sysRatesSet1));
            return update;
        } else {
            Company company = companyMapper.selectById(sysRatesSet.getRatesCompanyId());
            if (CheckEmptyUtil.isNotEmpty(company)) {
                sysRatesSet.setRatesCompanyName(company.getFullName());
            }
            int insert = sysRatesSetMapper.insert(sysRatesSet);
            List<SysRatesSet> o = sysRatesSetMapper.selectList(queryWrapper);
            SysRatesSet sysRatesSet1 = o.get(0);
            redisTemplate.opsForValue().set(RedisGroupPrefix.SYSRATES_SUFFIX + sysRatesSet1.getRatesCompanyId().toString(), JsonMapper.toJsonString(sysRatesSet1));
            return insert;
        }

    }

    @Override
    public int editFreighteMode(SysRatesSet sysRatesSet) {
        QueryWrapper<SysRatesSet> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rates_id", sysRatesSet.getRatesId());
        List<SysRatesSet> list = sysRatesSetMapper.selectList(queryWrapper);
        if (CheckEmptyUtil.isEmpty(list)) {
            throw new RuntimeException("此托运人公司的数据不存在，请确认");
        }
        SysRatesSet param = list.get(0);
        LambdaUpdateWrapper<SysRatesSet> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(SysRatesSet::getRatesId, param.getRatesId());
        SysRatesSet result = new SysRatesSet();
        result.setDirectPlanFlag(sysRatesSet.getDirectPlanFlag());
        result.setDirectPlanVehiclePrice(sysRatesSet.getDirectPlanVehiclePrice());
        result.setDriectPlanAmountType(sysRatesSet.getDriectPlanAmountType());
        result.setFixedPlanFlag(sysRatesSet.getFixedPlanFlag());
        result.setFixedPlanVehiclePrice(sysRatesSet.getFixedPlanVehiclePrice());
        result.setFixedPlanAmountType(sysRatesSet.getFixedPlanAmountType());
        result.setUpdateTime(new Date());
        Company company = companyMapper.selectById(param.getRatesCompanyId());
        if (CheckEmptyUtil.isNotEmpty(company)) {
            result.setRatesCompanyName(company.getFullName());
        }
        int update = sysRatesSetMapper.update(result, lambdaUpdateWrapper);
        List<SysRatesSet> o = sysRatesSetMapper.selectList(queryWrapper);
        SysRatesSet sysRatesSet1 = o.get(0);
        redisTemplate.opsForValue().set(RedisGroupPrefix.SYSRATES_SUFFIX + sysRatesSet1.getRatesCompanyId().toString(), JsonMapper.toJsonString(sysRatesSet1));
        return update;
    }

    @Override
    public SysRatesSet selectFreighteMode(String ratesCompanyId) {
        QueryWrapper<SysRatesSet> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("rates_company_id", ratesCompanyId);
        List<SysRatesSet> list = sysRatesSetMapper.selectList(queryWrapper);
        if (CheckEmptyUtil.isEmpty(list)) {
            throw new RuntimeException("此托运人公司的数据不存在，请确认");
        }
        return list.get(0);
    }

}
