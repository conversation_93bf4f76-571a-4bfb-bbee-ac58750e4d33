package com.lcdt.userinfo.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.userinfo.dto.CustomerDto;
import com.lcdt.userinfo.model.Customer;
import com.lcdt.userinfo.model.CustomerContact;
import com.lcdt.userinfo.rpc.CustomerRpcService;
import com.lcdt.userinfo.dao.customer.CustomerContactMapper;
import com.lcdt.userinfo.dao.customer.CustomerMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by ss on 2017/11/24.
 */
@Transactional
@Service
public class CustomerRpcServiceImpl  implements CustomerRpcService {

    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private CustomerContactMapper customerContactMapper;


    @Override
    public PageInfo customerList(CustomerDto dto) {
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        List<CustomerDto> customerDtos = customerMapper.customerList(dto);
        return new PageInfo(customerDtos);
    }

    @Override
    public Customer customerDetail(Long customerId, Long companyId) {
        return customerMapper.customerDetail(customerId, companyId);
    }

    @Override
    public int changeCustomerStatus(Customer customer) {
        UpdateWrapper<Customer> uw = new UpdateWrapper<>();
        uw.set("status", customer.getStatus());
        uw.eq("customer_id", customer.getCustomerId());
        uw.eq("company_id", customer.getCompanyId());
        return customerMapper.update(new Customer(), uw);
    }

    @Override
    public int addCustomer(Customer customer) {
        customer.setStatus((short)1);
        int r = customerMapper.insertCustomer(customer);
        if (r > 0 && CollectionUtils.isNotEmpty(customer.getCustomerContactList())) {
            addCustomerContact(customer, customer.getCustomerContactList());
        }
        return r;
    }


    private void addCustomerContact(Customer customer, List<CustomerContact> customerContactList) {
        customerContactList.stream().map(obj->{
            obj.setCustomerId(customer.getCustomerId());
            obj.setCompanyId(customer.getCompanyId());
            obj.setCreateDate(customer.getCreateDate());
            obj.setCreateId(customer.getCreateId());
            obj.setCreateName(customer.getCreateName());
            return obj;
        }).collect(Collectors.toList());
        customerContactMapper.batchAddCustomerContact(customerContactList);

    }

    @Override
    public int updateCustomer(Customer customer) {
        int r =  customerMapper.updateById(customer);
        if (r > 0 && CollectionUtils.isNotEmpty(customer.getCustomerContactList())) {
            customerContactMapper.delete(Wrappers.<CustomerContact>query().eq("customer_id", customer.getCustomerId()));//先删除所有客户
            addCustomerContact(customer, customer.getCustomerContactList());
        }
        return r;
    }

    @Override
    public PageInfo customerPullList(CustomerDto CustomerDto) {
        PageHelper.startPage(CustomerDto.getPageNo(), CustomerDto.getPageSize());
        return new PageInfo(customerMapper.customerPullList(CustomerDto));
    }


}
