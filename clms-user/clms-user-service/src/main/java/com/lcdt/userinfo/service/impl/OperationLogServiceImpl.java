package com.lcdt.userinfo.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.userinfo.dao.OperationLogMapper;
import com.lcdt.userinfo.model.OperationLog;
import com.lcdt.userinfo.service.OperationLogService;
import com.lcdt.util.HttpUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * @author: lyqishan
 * @date: 2020/4/21 15:02
 * @description:
 */
@Service
@Transactional
public class OperationLogServiceImpl implements OperationLogService {

    @Autowired
    private OperationLogMapper operationLogMapper;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Override
    public int addOperationLog(String operationTitle) {
        OperationLog operationLog = new OperationLog();
        operationLog.setOperationTitle(operationTitle);
        operationLog.setUserName(securityInfoGetter.getUserInfo().getRealName());
        operationLog.setUserId(securityInfoGetter.getUserInfo().getUserId());
        operationLog.setOperationTime(new Date());
        operationLog.setOperationIp(HttpUtils.getLocalIp(request));
        operationLog.setCompanyId(securityInfoGetter.getCompId());
        return operationLogMapper.insert(operationLog);
    }

    @Override
    public int addOperationLog(OperationLog operationLog) {
        return operationLogMapper.insert(operationLog);
    }

    @Override
    public PageInfo queryOperationLogList(Long companyId, String userName,Long userId, Date beginTime, Date endTime, Integer pageNo, Integer pageSize) {
        if(pageNo==null){
            pageNo=1;
        }
        if(pageSize==null){
            pageSize=10;
        }
        PageHelper.startPage(pageNo,pageSize);
        return new PageInfo(operationLogMapper.selectOperationList(companyId,userName,userId,beginTime,endTime));
    }
}
