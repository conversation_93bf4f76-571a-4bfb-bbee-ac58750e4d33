<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.UserCustomerMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.Customer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 24 10:56:38 CST 2017.
    -->
    <id column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="short_name" jdbcType="VARCHAR" property="shortName" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="client_types" jdbcType="VARCHAR" property="clientTypes" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="detail_address" jdbcType="VARCHAR" property="detailAddress" />
    <result column="group_ids" jdbcType="VARCHAR" property="groupIds" />
    <result column="group_names" jdbcType="VARCHAR" property="groupNames" />
    <result column="post_code" jdbcType="VARCHAR" property="postCode" />
    <result column="tel_no" jdbcType="VARCHAR" property="telNo" />
    <result column="fax" jdbcType="VARCHAR" property="fax" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="attachment_1" jdbcType="VARCHAR" property="attachment1" />
    <result column="attachment_2" jdbcType="VARCHAR" property="attachment2" />
    <result column="attachment_name1" jdbcType="VARCHAR" property="attachmentName1" />
    <result column="attachment_name2" jdbcType="VARCHAR" property="attachmentName2" />
    <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle" />
    <result column="registration_no" jdbcType="VARCHAR" property="registrationNo" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_no" jdbcType="VARCHAR" property="bankNo" />
    <result column="registration_address" jdbcType="VARCHAR" property="registrationAddress" />
    <result column="tel_no1" jdbcType="VARCHAR" property="telNo1" />
    <result column="invoice_remark" jdbcType="VARCHAR" property="invoiceRemark" />
    <result column="bind_comp_id" jdbcType="BIGINT" property="bindCpid" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="bind_company" jdbcType="VARCHAR" property="bindCompany" />
    <result column="link_man" jdbcType="VARCHAR" property="linkMan" />
    <result column="link_duty" jdbcType="VARCHAR" property="linkDuty" />
    <result column="link_tel" jdbcType="VARCHAR" property="linkTel" />
    <result column="link_email" jdbcType="VARCHAR" property="linkEmail" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="collection_ids" jdbcType="VARCHAR" property="collectionIds" />
    <result column="collection_names" jdbcType="VARCHAR" property="collectionNames" />
  </resultMap>
  <select id="selectByPrimaryKey"  resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 24 10:56:38 CST 2017.
    -->
    select customer_id, customer_name, short_name, customer_code, client_types, province,
    city, county, detail_address, group_ids, group_names, post_code, tel_no, fax, remark,
    attachment_1, attachment_2,attachment_name1,attachment_name2, invoice_title, registration_no, bank_name, bank_no, registration_address,
    tel_no1, invoice_remark, bind_comp_id, status, bind_company, link_man, link_duty, link_tel,
    link_email, company_id,collection_ids,collection_names
    from uc_customer
    where customer_id = #{customerId} and company_id = #{companyId}
  </select>


  <update id="updateByPrimaryKey" parameterType="com.lcdt.userinfo.model.Customer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 24 10:56:38 CST 2017.
    -->
    update uc_customer
    set group_names = #{groupNames,jdbcType=VARCHAR},
    group_ids = #{groupIds,jdbcType=VARCHAR}
    where customer_id = #{customerId,jdbcType=BIGINT} and company_id = #{companyId,jdbcType=BIGINT}
  </update>




  <select id="selectByCondition"  parameterType="java.util.Map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 24 10:56:38 CST 2017.
    -->
    select customer_id, customer_name, short_name, customer_code, client_types, province,
    city, county, detail_address, group_ids, group_names, post_code, tel_no, fax, remark,
    attachment_1, attachment_2,attachment_name1,attachment_name2, invoice_title, registration_no, bank_name, bank_no, registration_address,
    tel_no1, invoice_remark, bind_comp_id, status, bind_company, link_man, link_duty, link_tel,
    link_email, company_id,collection_ids,collection_names
    from uc_customer
    <where>
      company_id = #{companyId,jdbcType=BIGINT}
      <if test="groupIds!=null and groupIds!=''">
        and  ${groupIds}
      </if>
      <if test="bindCompId!=null and bindCompId!=''">
        and bind_comp_id = #{bindCompany,jdbcType=BIGINT}
      </if>
      <if test="complexContition!=null and complexContition!=''">
        and (customer_name like CONCAT(#{complexContition,jdbcType=VARCHAR}, '%', '%') or short_name like CONCAT(#{complexContition,jdbcType=VARCHAR}, '%', '%')  or customer_code like CONCAT(#{complexContition,jdbcType=VARCHAR}, '%', '%') )
      </if>
    </where>
    order by customer_id
  </select>



</mapper>