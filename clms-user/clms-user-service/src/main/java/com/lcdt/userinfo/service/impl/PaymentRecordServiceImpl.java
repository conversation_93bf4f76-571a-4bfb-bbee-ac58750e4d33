package com.lcdt.userinfo.service.impl;

import com.lcdt.userinfo.dao.PaymentRecordMapper;
import com.lcdt.userinfo.model.PaymentRecord;
import com.lcdt.userinfo.service.PaymentRecordService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PaymentRecordServiceImpl implements PaymentRecordService {

    @Autowired
    private PaymentRecordMapper paymentRecordMapper;

    @Override
    public int savePaymentRecord(PaymentRecord paymentRecord) {
        return paymentRecordMapper.insert(paymentRecord);
    }
}
