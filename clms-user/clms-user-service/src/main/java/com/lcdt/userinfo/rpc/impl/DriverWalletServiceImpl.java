package com.lcdt.userinfo.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lcdt.pay.abc.dto.AccountBook;
import com.lcdt.pay.abc.enums.InterFaceEnum;
import com.lcdt.pay.abc.service.AbcApiService;
import com.lcdt.traffic.model.InterfaceLog;
import com.lcdt.traffic.service.DriverRpcService;
import com.lcdt.traffic.service.InterfaceLogRpcService;
import com.lcdt.traffic.vo.ConstantVO;
import com.lcdt.userinfo.dao.DriverWalletMapper;
import com.lcdt.userinfo.dao.DriverWithdrawalLogMapper;
import com.lcdt.userinfo.dto.DriverAccountDto;
import com.lcdt.userinfo.dto.DriverWalletDto;
import com.lcdt.userinfo.model.DriverWallet;
import com.lcdt.userinfo.model.DriverWithdrawalLog;
import com.lcdt.userinfo.rpc.IDriverWalletRpcService;
import com.lcdt.util.CheckEmptyUtil;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import static com.lcdt.traffic.vo.ConstantVO.*;

/**
 * Created by ybq on 2020/7/8 9:16
 */
@Slf4j
@Service
@Transactional
public class DriverWalletServiceImpl implements IDriverWalletRpcService {


    @Autowired
    private DriverWalletMapper driverWalletMapper;


    @Lazy
    @Autowired
    DriverRpcService driverRpcService;

    @Autowired
    private InterfaceLogRpcService interfaceLogRpcService;

    @Autowired
    private AbcApiService abcApiService;

    @Autowired
    private DriverWithdrawalLogMapper driverWithdrawalLogMapper;

    @Override
    public int addDriverWallet(DriverWallet driverWallet) {
        return driverWalletMapper.insert(driverWallet);
    }

    @Override
    public int updateDriverWallet(DriverWallet driverWallet) {
        return driverWalletMapper.updateById(driverWallet);
    }

    @Override
    public DriverWallet queryByDriverId(Long driverId) {
        QueryWrapper qw = new QueryWrapper();
        qw.eq("driver_id", driverId);
        return driverWalletMapper.selectOne(qw);
    }

    @Override
    public DriverWallet queryByDriverIdAndAffiliatedPlatform(Long driverId, String affiliatedPlatform) {
        QueryWrapper qw = new QueryWrapper();
        qw.eq("driver_id", driverId);
        qw.eq("affiliated_platform", affiliatedPlatform);
        return driverWalletMapper.selectOne(qw);
    }

    @Override
    public DriverWallet queryByOrderNo(String orderNo) {
        return driverWalletMapper.selectOne(new QueryWrapper<DriverWallet>().lambda()
                .eq(DriverWallet::getDriverOrderNo, orderNo));
    }


    @Override
    public List<DriverWallet> queryDriverAbcAccount(Long userId) {
        QueryWrapper qw = new QueryWrapper();
        qw.eq("driver_id", userId);
        List<DriverWallet> driverWalletList = driverWalletMapper.selectList(qw);
        if (CheckEmptyUtil.isEmpty(driverWalletList)) {
            return null;
        }
        for (DriverWallet driverWallet : driverWalletList) {
            //获取下司机的提现记录里有没有问题数据，没有的话使用账簿余额
            DriverWithdrawalLog driverWithdrawalLog = new DriverWithdrawalLog();
            driverWithdrawalLog.setDriverId(userId);
            List<DriverWithdrawalLog> driverWithdrawalLogs = driverWithdrawalLogMapper.selectArray(driverWithdrawalLog);
            if (CheckEmptyUtil.isEmpty(driverWithdrawalLogs)) {
                //根据账簿号获取余额
                try {
                    InterfaceLog interfaceLog = new InterfaceLog();
                    AccountBook accountBookAccount = abcApiService.getAccountBookAccount(driverWallet.getAffiliatedPlatform(), driverWallet.getAbNo());
                    if (FLAG_0000.equalsIgnoreCase(accountBookAccount.getCode())) {
                        interfaceLog.setTransFlag(FLAG_Y);
                        String bal = accountBookAccount.getBal();
                        BigDecimal balBig = new BigDecimal(bal);
                        BigDecimal ca = balBig.multiply(new BigDecimal("100"));
                        Long _cost = ca.longValue();
                        driverWallet.setDriverWalletBalance(_cost);
                        driverWallet.setAbcBalance(_cost);
                    } else {
                        interfaceLog.setTransFlag(FLAG_N);
                    }
                    //5. 接口出入参插入接口表
                    interfaceLog.setDestSystem(ConstantVO.ABCDesSystem);
                    interfaceLog.setDataType(InterFaceEnum.QUERYBALANCEACCOUNT.getInterfaceCode());
                    interfaceLog.setDataName(InterFaceEnum.QUERYBALANCEACCOUNT.getInterfaceName());
                    interfaceLog.setData(accountBookAccount.getReqMsg());
                    interfaceLog.setOtherId(driverWallet.getDriverId());
                    interfaceLog.setTransTime(new Timestamp(System.currentTimeMillis()));
                    interfaceLog.setResponseMsg(accountBookAccount.getResMsg());
                    interfaceLog.setCreateBy(driverWallet.getDriverId().toString());
                    interfaceLog.setCreateDate(new Date());
                    interfaceLogRpcService.insertInterfaceLog(interfaceLog);
                    driverWalletMapper.updateAbcAmountById(driverWallet.getDriverWalletId(), driverWallet.getAbcBalance());
                } catch (Exception ex) {
                    log.error("调用农行查询司机余额错误，error：", ex);
                    return null;
                }
            }
        }
        return driverWalletList;
    }

    @Override
    public int updateDriverAbcWallet(Long driverWalletId, Long abcAmount) {
        return driverWalletMapper.updateAbcAmountById(driverWalletId, abcAmount);
    }

    @Override
    public int updateAbcBalanceByDriverId(Long driverId, Long amount) {
        return driverWalletMapper.updateAbcAmountByDriverId(driverId, amount);
    }

    @Override
    public int updateAbcBalanceByDriverIdAndAffiliatedPlatform(Long driverId, String affiliatedPlatform, Long amount) {
        return driverWalletMapper.updateAbcAmountByDriverIdAndAffiliatedPlatform(driverId, affiliatedPlatform, amount);

    }

    @Override
    public int editDriverWallet(DriverWalletDto driverWalletDto) {
        QueryWrapper qw = new QueryWrapper();
        qw.eq("driver_id", driverWalletDto.getDriverId());
        DriverWallet driverWallet = driverWalletMapper.selectOne(qw);
        if (CheckEmptyUtil.isNotEmpty(driverWallet)) {
            driverWallet.setIsAbcBank(driverWalletDto.getIsAbcBank());
            driverWallet.setDriverBankCardNoSelf(driverWalletDto.getDriverBankCardNoSelf());
            if (driverWallet.getIsAbcBank() == 0) {
                driverWallet.setDriverOpenBankCode(org.apache.commons.lang.StringUtils.EMPTY);
                driverWallet.setDriverOpenBankName(org.apache.commons.lang.StringUtils.EMPTY);
            } else {
                driverWallet.setDriverOpenBankCode(driverWalletDto.getDriverOpenBankCode());
                driverWallet.setDriverOpenBankName(driverWalletDto.getDriverOpenBankName());
            }
            return driverWalletMapper.updateById(driverWallet);
        } else {
            return 0;
        }

    }


    @Override
    public IPage<DriverAccountDto> queryDriverAccount(IPage<?> page, DriverAccountDto dto) {
        return driverWalletMapper.selectDriverAccount(page, dto);
    }
}
