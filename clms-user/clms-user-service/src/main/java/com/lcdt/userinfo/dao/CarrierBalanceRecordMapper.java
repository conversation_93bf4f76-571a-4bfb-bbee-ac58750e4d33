package com.lcdt.userinfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.userinfo.dto.CarrierBalanceRecordDto;
import com.lcdt.userinfo.dto.CarrierBalanceRecordDto1;
import com.lcdt.userinfo.dto.CarrierBalanceStatisticalDataDto;
import com.lcdt.userinfo.model.CarrierBalanceRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface CarrierBalanceRecordMapper extends BaseMapper<CarrierBalanceRecord> {

    List<CarrierBalanceRecordDto1> queryList(CarrierBalanceRecordDto carrierBalanceRecordDto);

    /**
     * 根据运单号查询，对应的电子凭证
     *
     * @param waybillCode
     * @return
     */
    Map<String, Object> queryByWaybillCode(String waybillCode);

    /**
     * 查询服务相关内容
     *
     * @param sfList
     * @return
     */
    List<CarrierBalanceRecord> selectServiceFee(@Param("sfList") List<String> sfList);

    Map<String, Object> queryReceiptNoByWaybillCode(String waybillCode);

    int selectConutByOrderNo(@Param("orderNo") String orderNo);

    int selectCountByOutTradeNo(@Param("outTradeNo") String outTradeNo);

    CarrierBalanceStatisticalDataDto selectStatisticalData();


    /**
     * 支出收入统计
     */
    Map<String, Integer> payStatistics();

    Map<String, Integer> payStatisticsAh();

}