<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.CarrierBalanceRecordMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.CarrierBalanceRecord">
        <id column="record_id" jdbcType="BIGINT" property="recordId"/>
        <result column="record_time" jdbcType="TIMESTAMP" property="recordTime"/>
        <result column="relate_order_no" jdbcType="VARCHAR" property="relateOrderNo"/>
        <result column="payment_party_phone" jdbcType="VARCHAR" property="paymentPartyPhone"/>
        <result column="payment_party_phone" jdbcType="VARCHAR" property="paymentPartyPhone"/>
        <result column="payment_party_company_id" jdbcType="BIGINT" property="paymentPartyCompanyId"/>
        <result column="payment_party_company_name" jdbcType="VARCHAR" property="paymentPartyCompanyName"/>
        <result column="payment_party_driver_id" jdbcType="BIGINT" property="paymentPartyDriverId"/>
        <result column="payment_party_driver_name" jdbcType="VARCHAR" property="paymentPartyDriverName"/>
        <result column="bank_statement_type" jdbcType="SMALLINT" property="bankStatementType"/>
        <result column="bank_statement_amount" jdbcType="DECIMAL" property="bankStatementAmount"/>
        <result column="bank_statement_current_amount" jdbcType="DECIMAL" property="bankStatementCurrentAmount"/>
        <result column="record_remark" jdbcType="VARCHAR" property="recordRemark"/>
        <result column="record_operator_name" jdbcType="VARCHAR" property="recordOperatorName"/>
        <result column="record_operator_phone" jdbcType="VARCHAR" property="recordOperatorPhone"/>
        <result column="record_operator_id" jdbcType="INTEGER" property="recordOperatorId"/>
        <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo"/>
        <result column="jrn_no" jdbcType="VARCHAR" property="jrnNo"/>
        <result column="agent_id" jdbcType="BIGINT" property="agentId"/>
        <result column="affiliated_platform" jdbcType="VARCHAR" property="affiliatedPlatform" />
    </resultMap>
    <sql id="Base_Column_List">
        record_id
        ,record_time,relate_order_no,
    payment_party_phone,payment_party_company_id,payment_party_driver_id,payment_party_company_name,
    payment_party_driver_name,bank_statement_type,bank_statement_amount,record_type,
    bank_statement_current_amount,record_remark,record_operator_name,
    record_operator_phone,record_operator_id,out_trade_no,jrn_no,agent_id,affiliated_platform
    </sql>
    <select id="queryList" resultType="com.lcdt.userinfo.dto.CarrierBalanceRecordDto1">
        select
        <include refid="Base_Column_List"/>
        , t2.payee_name as agent_name, t2.payee_phone as agent_phone
        from uc_carrier_balance_record t left join tr_payee_account t2
        on t.agent_id = t2.oa_id
        <where>
            1=1
            <if test="bankStatementType != null ">
                and t.bank_statement_type=#{bankStatementType,jdbcType=SMALLINT}
            </if>

            <if test="serviceType != null and serviceType!=''">
                and t.bank_statement_type = 5
            </if>
            <if test="serviceType == null or serviceType==''">
                and t.bank_statement_type != 5
            </if>
            <if test="billParms != null and billParms!='' ">
                and (t.record_remark like concat(concat('%',#{billParms,jdbcType=VARCHAR}),'%') or
                t.out_trade_no like concat(concat('%',#{billParms,jdbcType=VARCHAR}),'%') or
                t.relate_order_no like concat(concat('%',#{billParms,jdbcType=VARCHAR}),'%')
                )
            </if>
            <if test="payeeName != null and payeeName != ''">
                and (t.payment_party_driver_name like concat(concat('%',#{payeeName,jdbcType=VARCHAR}),'%') or
                t.payment_party_company_name like concat(concat('%',#{payeeName,jdbcType=VARCHAR}),'%')
                )
            </if>
            <if test="agentName!=null and agentName!=''">
                and (t2.payee_name like concat('%',#{agentName},'%') or
                t2.payee_phone like concat('%',#{agentName},'%'))
            </if>

            <if test="recordOperatorName != null and recordOperatorName != ''">
                and t.record_operator_name=#{recordOperatorName,jdbcType=VARCHAR}
            </if>
            <if test="affiliatedPlatform != null and affiliatedPlatform != ''">
                and t.affiliated_platform=#{affiliatedPlatform,jdbcType=VARCHAR}
            </if>
            <if test="createBeginDt!=null and createBeginDt!=''">
                and t.record_time &gt;= CONCAT(STR_TO_DATE(#{createBeginDt,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
            </if>
            <if test="createEndDt!=null and createEndDt!=''">
                and t.record_time &lt;= CONCAT(STR_TO_DATE(#{createEndDt,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
            </if>
            order by t.record_id desc
        </where>

    </select>
    <select id="selectServiceFee" resultType="com.lcdt.userinfo.model.CarrierBalanceRecord">
        select relate_order_no,bank_statement_current_amount from uc_carrier_balance_record
        <where>
            <if test="sfList != null and sfList.size>0">
                and bank_statement_type = 5
                and relate_order_no in
                <foreach collection="sfList" item="orderNo" index="i" open="(" close=")" separator=",">
                    #{orderNo}
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryByWaybillCode" resultType="java.util.HashMap" parameterType="string">
        select tc.record_remark as waybill_code,te.oss_url
        from uc_electronic_receipt te,uc_carrier_balance_record tc
        <where>
            te.order_no= tc.relate_order_no and tc.bank_statement_type=2 and tc.record_remark=#{waybillCode}
        </where>
    </select>

    <select id="queryReceiptNoByWaybillCode" resultType="java.util.HashMap" parameterType="string">
        select tc.record_remark as waybill_code,te.oss_url,te.receipt_no
        from uc_electronic_receipt te,uc_carrier_balance_record tc
        <where>
            te.order_no= tc.relate_order_no and tc.bank_statement_type=2 and tc.record_remark=#{waybillCode}
        </where>
    </select>

    <select id="selectConutByOrderNo" resultType="int">
        select count(1) from uc_carrier_balance_record where relate_order_no = #{orderNo}
    </select>

    <select id="selectCountByOutTradeNo" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(1) from uc_carrier_balance_record where out_trade_no = #{outTradeNo}
    </select>
    <select id="selectStatisticalData" resultType="com.lcdt.userinfo.dto.CarrierBalanceStatisticalDataDto">
        SELECT
            sum(bank_statement_current_amount) AS totalAmount,
            sum( CASE WHEN bank_statement_type IN ( '1', '3','5' ) THEN bank_statement_current_amount ELSE 0 END ) AS incomeAmount,
            sum( CASE WHEN bank_statement_type IN ( '2', '4' ) THEN bank_statement_current_amount ELSE 0 END ) AS expenditureAmount
        FROM
            uc_carrier_balance_record;
    </select>
    <select id="payStatistics" resultType="java.util.Map">
        SELECT
            sum(
                    IF
                    ( bank_statement_type = 1, bank_statement_current_amount - bank_statement_amount, 0 )) AS sd_payment_income,
            sum(
                    IF
                    ( bank_statement_type = 2, bank_statement_amount - bank_statement_current_amount, 0 )) AS sd_payment_outlay,
            sum(
                    IF
                    ( bank_statement_type = 3, bank_statement_current_amount - bank_statement_amount, 0 )) AS sd_payment_recharge
        FROM
            uc_carrier_balance_record
        WHERE
            affiliated_platform = 'sd'
    </select>
    <select id="payStatisticsAh" resultType="java.util.Map">
        SELECT
            sum(
                    IF
                    ( bank_statement_type = 1, bank_statement_current_amount - bank_statement_amount, 0 )) AS ah_payment_income,
            sum(
                    IF
                    ( bank_statement_type = 2, bank_statement_amount - bank_statement_current_amount, 0 )) AS ah_payment_outlay,
            sum(
                    IF
                    ( bank_statement_type = 3, bank_statement_current_amount - bank_statement_amount, 0 )) AS ah_payment_recharge
        FROM
            uc_carrier_balance_record
        WHERE
            affiliated_platform = 'ah'
    </select>
</mapper>