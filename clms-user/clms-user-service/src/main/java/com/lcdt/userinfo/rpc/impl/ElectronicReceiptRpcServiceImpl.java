package com.lcdt.userinfo.rpc.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.lcdt.common.config.AliyunOssConfig;
import com.lcdt.pay.abc.dto.AbcFile;
import com.lcdt.pay.abc.dto.AbcFileParam;
import com.lcdt.pay.abc.service.AbcApiService;
import com.lcdt.userinfo.dao.ElectronicReceiptMapper;
import com.lcdt.userinfo.dto.AliyunOssDto;
import com.lcdt.userinfo.model.ElectronicReceipt;
import com.lcdt.userinfo.rpc.CarrierBalanceRpcService;
import com.lcdt.userinfo.rpc.ElectronicReceiptRpcService;
import com.lcdt.userinfo.utils.HttpUtil;
import com.lcdt.util.ResponseCodeVO;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.lcdt.traffic.vo.ConstantVO.FLAG_0000;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ElectronicReceiptRpcServiceImpl implements ElectronicReceiptRpcService {

    @Autowired
    private ElectronicReceiptMapper electronicReceiptMapper;

    @Autowired
    private CarrierBalanceRpcService carrierBalanceRpcService;

    @Autowired
    private AbcApiService abcApiService;

    @Autowired
    private RedissonClient redissonClient;


    @Value("${isDebug}")
    private Boolean isDebug;

    @Value("${abc.oss-upload-url}")
    private String abcOssUploadUrl;

    @Autowired
    private AliyunOssConfig aliyunOssConfig;


    /**
     * 创建电子凭证申请时，创建响应记录
     *
     * @param electronicReceipt
     * @return
     */
    @Override
    public int create(ElectronicReceipt electronicReceipt) {
        return electronicReceiptMapper.insert(electronicReceipt);
    }

    /**
     * 电子凭证消息通知调用更新相应的电子凭证的cdn及上传oss等操作
     *
     * @param electronicReceipt
     * @return
     */
    @Override
    public int update(ElectronicReceipt electronicReceipt) {
        return electronicReceiptMapper.update(electronicReceipt, new UpdateWrapper<ElectronicReceipt>().lambda().eq(ElectronicReceipt::getReceiptNo, electronicReceipt.getReceiptNo()));
    }

    /**
     * 根据orderNo查询对应的电子凭证信息
     *
     * @param outTradeNo
     * @return
     */
    @Override
    public ElectronicReceipt queryByOutTradeNo(String outTradeNo) {
        return electronicReceiptMapper.selectOne(new QueryWrapper<ElectronicReceipt>().lambda()
                .eq(ElectronicReceipt::getOutTradeNo, outTradeNo).isNotNull(ElectronicReceipt::getOssUrl));
    }


    //    @Async("taskExecutor")
    @Override
    public void abcReceiptApply(String payerAccount, String payeeAccount, String amount, String tradeDate, String jrnNo, String outTradeNo) {
        AbcFileParam abcFileParam = new AbcFileParam();
        abcFileParam.setThisAccountNo(payerAccount);
        abcFileParam.setOtherAccountNo(payeeAccount);
        // 添加负号代表是 支出
        abcFileParam.setAmount("-" + amount);
        abcFileParam.setTradeDate(tradeDate);
        abcFileParam.setJrnNO(jrnNo);
        try {
            Thread.sleep(20);
            AbcFile abcFile = abcApiService.electronicReceipt(abcFileParam);
            if (abcFile.getCode().equals(FLAG_0000)) {
                // 等待2s再去触发上传oss操作，避免因文件下载导致上传失败
                Thread.sleep(2000);
                HashMap<String, Object> params = new HashMap<>();
                params.put("rename", "abc_" + jrnNo);
                params.put("originName", abcFile.getFileName());
                params.put("pathName", "electronic_receipt/");
                params.put("fileEnd", ".pdf");
                params.put("isDebug", isDebug);
                AliyunOssDto aliyunOssDto = new AliyunOssDto();
                BeanUtils.copyProperties(aliyunOssConfig,aliyunOssDto);
                params.put("ossConfig", JSONObject.toJSONString(aliyunOssDto));
                String msg = HttpUtil.doPost(abcOssUploadUrl + "/oss/upload", params);
                JSONObject obj = JSONObject.parseObject(msg);
                if (obj.getInteger(ResponseCodeVO.CODE).equals(ResponseCodeVO.SUCCESS_CODE)) {
                    ElectronicReceipt electronicReceipt = new ElectronicReceipt();
                    electronicReceipt.setOrderNo(jrnNo);
                    // 农行不直接返回电子回单编号，这里保存该笔交易的日志号
                    electronicReceipt.setReceiptNo(jrnNo);
                    electronicReceipt.setOssUrl(obj.getString(ResponseCodeVO.DATA));
                    electronicReceipt.setStatus(2);
                    electronicReceipt.setOutTradeNo(outTradeNo);
                    electronicReceipt.setCreateTime(new Date());
                    electronicReceiptMapper.insert(electronicReceipt);
                    log.info("电子回单保存成功");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) throws ParseException {

/*        // 方法一
        System.out.println(System.currentTimeMillis());

        // 方法二
        Date date = new Date();
        long t = date.getTime();
        System.out.println();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date1 = simpleDateFormat.parse("2021-03-26 17:25:50.0");
        long t1 = date1.getTime();
        System.out.println(System.currentTimeMillis() - t1);
        System.out.println(10 * 60 * 1000);*/
//        System.out.println(getExtensionName("202012621921037517.pdf?Expires=1608347064&OSSAccessKeyId=LTAI4G8swFQ7Hz8m6FCpjSEH&Signature=c8sDjlbXQAEfJwYyYQa0jLFwWGQ%3D"));
    }
}
