<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.DataReportingLogMapper">
  <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.DataReportingLog">
    <id column="dr_id" jdbcType="BIGINT" property="drId" />
    <result column="dr_document_name" jdbcType="VARCHAR" property="drDocumentName" />
    <result column="dr_message_reference_number" jdbcType="VARCHAR" property="drMessageReferenceNumber" />
    <result column="dr_message_sending_date_time" jdbcType="TIMESTAMP" property="drMessageSendingDateTime" />
    <result column="dr_sender_code" jdbcType="VARCHAR" property="drSenderCode" />
    <result column="dr_ipc_type" jdbcType="VARCHAR" property="drIpcType" />
    <result column="dr_result_code" jdbcType="INTEGER" property="drResultCode" />
    <result column="dr_result_message" jdbcType="VARCHAR" property="drResultMessage" />
    <result column="dr_status" jdbcType="INTEGER" property="drStatus" />
    <result column="dr_status" jdbcType="VARCHAR" property="drOperator" />
    <result column="dr_operator_id" jdbcType="BIGINT" property="drOperatorId" />
    <result column="dr_time" jdbcType="TIMESTAMP" property="drTime" />
    <result column="dr_encrypted_content" jdbcType="LONGVARCHAR" property="drEncryptedContent" />
    <result column="dr_dealwith_time" jdbcType="TIMESTAMP" property="drDealwithTime" />
  </resultMap>
  <sql id="Base_Column_List">
    dr_id,dr_document_name,dr_message_reference_number,dr_message_sending_date_time,
    dr_sender_code,dr_ipc_type,dr_result_code,
    dr_result_message,dr_status,dr_status,dr_time,dr_dealwith_time
  </sql>
  <select id="queryList" parameterType="com.lcdt.userinfo.dto.DataReportingLogDto" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from uc_data_reporting_log t
    <where>
      1=1
      <if test="drStatus != null">
        and t.dr_status=#{drStatus,jdbcType=INTEGER}
      </if>

      <if test="drDocumentName != null">
        and t.dr_document_name=#{drDocumentName,jdbcType=VARCHAR}
      </if>

      <if test="createBeginDt!=null">
        and t.dr_time &gt;= CONCAT(STR_TO_DATE(#{createBeginDt,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
      </if>
      <if test="createEndDt!=null">
        and t.dr_time &lt;= CONCAT(STR_TO_DATE(#{createEndDt,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
      </if>

      <if test="dealwithBeginDt!=null">
        and t.dr_dealwith_time &gt;= CONCAT(STR_TO_DATE(#{dealwithBeginDt,jdbcType=VARCHAR},'%Y-%m-%d'),' ','00:00:00')
      </if>
      <if test="dealwithEndDt!=null">
        and t.dr_dealwith_time &lt;= CONCAT(STR_TO_DATE(#{dealwithEndDt,jdbcType=VARCHAR},'%Y-%m-%d'),' ','23:59:59')
      </if>

      order by t.dr_id desc
    </where>

  </select>
</mapper>