package com.lcdt.userinfo.rpc.impl;

import com.github.pagehelper.PageInfo;
import com.lcdt.userinfo.dto.CompanyQueryDto;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.rpc.CompanyManagerRpcService;
import com.lcdt.userinfo.service.CompanyManagerService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @author: lyqishan
 * @date: 2019-04-24 11:05
 * @description:
 */
@Transactional
@Service
public class CompanyManagerRpcServiceImpl implements CompanyManagerRpcService {

    @Autowired
    private CompanyManagerService companyManagerService;

    @Override
    public PageInfo queryCompanyListByCompanyDto(CompanyQueryDto companyQueryDto) {
        return companyManagerService.queryCompanyListByCompanyDto(companyQueryDto);
    }

    public List<Company> selectByCompanyIds(List<Long> companyIds){
        return companyManagerService.selectByCompanyIds(companyIds);
    }
}
