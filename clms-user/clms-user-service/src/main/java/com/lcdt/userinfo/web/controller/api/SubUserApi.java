package com.lcdt.userinfo.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.common.component.RedisCache;
import com.lcdt.common.constant.RedisGroupPrefix;
import com.lcdt.notify.rpcservice.ValidCodeService;
import com.lcdt.userinfo.dto.SubUserDto;
import com.lcdt.userinfo.dto.UserDto;
import com.lcdt.userinfo.model.Menu;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.rpc.GroupRpcService;
import com.lcdt.userinfo.service.SubUserService;
import com.lcdt.userinfo.service.UserService;
import com.lcdt.userinfo.web.dto.PageResultDto;
import com.lcdt.util.ResponseJsonUtils;
import com.lcdt.util.validate.ValidateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
/**
 * 子账号管理接口
 */
@RestController
@RequestMapping("/subuser")
@Slf4j
public class SubUserApi {

    @Autowired
    private SubUserService subUserService;

    @Autowired
    private UserService userService;

    @Autowired
    private ValidCodeService validCodeService;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private GroupRpcService groupRpcService;

    @Autowired
    private RedisCache redisCache;


    /**
     * 子账号列表
     *
     * @param userDto 查询参数
     * @return 子账号列表
     */
    @GetMapping("/pagelist")
    public PageResultDto roleList(UserDto userDto) {
        List<UserDto> userDtos = subUserService.selectByCondition(userDto);
        if (!CollectionUtils.isEmpty(userDtos)) {//子账户列表增加项目组
            userDtos.stream().map(obj -> {
                obj.setGroups(groupRpcService.groupList4UserId(obj.getUserId(), securityInfoGetter.getCompId()));
                return obj;
            }).collect(Collectors.toList());
        }
        PageResultDto<UserDto> userDtoPageResultDto = new PageResultDto<>(userDtos);
        return userDtoPageResultDto;
    }

    /**
     * 新增子账号
     * 同时设置账号的角色
     *
     * @param subUserDto 子账号信息
     * @param request HTTP请求
     * @return 新增结果
     */
    @PostMapping("/add")
    public JSONObject addSubUser(@RequestBody SubUserDto subUserDto, HttpServletRequest request) {
        int rows = subUserService.addSubUser(subUserDto);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("添加成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("添加失败");
        }

    }

    /**
     * 发送验证码
     *
     * @param request HTTP请求
     * @param phone 手机号
     * @return 发送结果
     */
    @PostMapping("/sendacaptch")
    @ResponseBody
    public JSONObject getCaptcha(HttpServletRequest request, String phone) {
        String msg = "";
        int code = -1;
        if (userService.isPhoneBeenRegister(phone)) {
            throw new ValidateException("手机号" + phone + "已注册！");
        } else {
            validCodeService.sendValidCode("addSubUser", 60 * 15, phone);
            redisCache.setCacheObject(RedisGroupPrefix.ADD_SUB_USER_RETRY + phone, 1, 60 * 5, TimeUnit.SECONDS);
            code = 0;
            msg = "发送成功！";
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("message", msg);
        jsonObject.put("code", code);
        return jsonObject;
    }

    /**
     * 编辑子账号
     *
     * @param subUserDto 子账号信息
     * @return 编辑结果
     */
    @PostMapping("/edit")
    public JSONObject editSubUser(@RequestBody SubUserDto subUserDto) {
        int rows = subUserService.editSubUser(subUserDto);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("修改成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("修改失败");
        }
    }


    /**
     * 删除子账号
     *
     * @param userId 用户ID
     * @return 删除结果
     */
    @PostMapping("del")
    public JSONObject delSubUser(Long userId) {
        int rows = subUserService.delSubUser(userId);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("删除成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("删除失败");
        }
    }

    /**
     * 子账号启用禁用
     *
     * @param user 用户信息
     * @return 设置结果
     */
    @PostMapping("/setstatus")
    public JSONObject setStatus(User user) {
        int rows = subUserService.setStatus(user);
        if (rows > 0) {
            return ResponseJsonUtils.successResponseJsonWithoutData("设置成功");
        } else {
            return ResponseJsonUtils.failedResponseJsonWithoutData("设置失败");
        }

    }

    /**
     * 获取用户权限及菜单
     *
     * @return 权限及菜单列表
     */
    @GetMapping("/getPermsInfo")
    public JSONObject getPermsInfo() {
        Long companyId = securityInfoGetter.getCompId();
        Long userId = securityInfoGetter.getUserInfo().getUserId();
        List<Menu> menuList = subUserService.getUserPerms(userId, companyId);
        return ResponseJsonUtils.successResponseJson(menuList);
    }

}
