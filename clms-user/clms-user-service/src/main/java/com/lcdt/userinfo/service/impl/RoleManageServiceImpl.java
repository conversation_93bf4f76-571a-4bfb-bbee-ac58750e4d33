package com.lcdt.userinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.lcdt.security.exception.BaseException;
import com.lcdt.userinfo.dao.RoleMapper;
import com.lcdt.userinfo.dao.RolePermsMapper;
import com.lcdt.userinfo.dao.RoleUserMapper;
import com.lcdt.userinfo.dto.RoleDto;
import com.lcdt.userinfo.dto.RolePermsDto;
import com.lcdt.userinfo.model.Role;
import com.lcdt.userinfo.model.RolePerms;
import com.lcdt.userinfo.model.RoleUser;
import com.lcdt.userinfo.service.RoleManageService;
import com.lcdt.util.CheckEmptyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RoleManageServiceImpl implements RoleManageService {

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private RoleUserMapper roleUserMapper;

    @Autowired
    private RolePermsMapper rolePermsMapper;

    @Override
    public List<RoleDto> selectPage(RoleDto role) {
        PageHelper.startPage(role.getPageNo(), role.getPageSize());
        return roleMapper.selectMyPage(role);
    }

    @Override
    public int delRole(Long roleId) {
        int rows = 0;
        /**
         * 1.判断是否有用户关联了该角色，有则无法删除
         * 2.删除角色主表信息
         * 3.删除角色菜单关联信息
         *
         */
        // 判断该角色是否有关联用户
        Long count = roleUserMapper.selectCount(new QueryWrapper<RoleUser>().lambda()
                .eq(RoleUser::getRoleId, roleId));
        if (count > 0) {
            throw new BaseException("该角色有关联的用户，无法删除");
        } else {
            // 删除角色主信息
            rows += roleMapper.deleteById(roleId);
            if (rows > 0) {
                // 删除角色菜单关联信息
                // todo
            }
        }
        return rows;
    }

    @Override
    public int setPerms(RolePermsDto rolePermsDto) {
        // 先删除原来的关系权限，再重新插入
        int rows = rolePermsMapper.delete(new UpdateWrapper<RolePerms>().lambda()
                .eq(RolePerms::getRoleId, rolePermsDto.getRoleId()));
        rows += rolePermsMapper.insertBatch(rolePermsDto);
        return rows;
    }

    @Override
    public int setStatus(Role role) {
        int rows = 0;
        // 判断该角色是否有关联用户
        Long count = roleUserMapper.selectCount(new QueryWrapper<RoleUser>().lambda()
                .eq(RoleUser::getRoleId, role.getRoleId()));
        if (count > 0) {
            throw new BaseException("该角色有关联的用户，无法禁用");
        } else {
            // 禁用角色
            rows += roleMapper.updateById(role);

        }
        return rows;
    }

    @Override
    public List<Role> selectByUser(Long userId) {
        List<RoleUser> roleUsers = roleUserMapper.selectList(new QueryWrapper<RoleUser>().lambda()
                .eq(RoleUser::getUserId, userId));
        if(CheckEmptyUtil.isNotEmpty(roleUsers)){
            List<Long> collect = roleUsers.stream().map(RoleUser::getRoleId).collect(Collectors.toList());
            LambdaQueryWrapper<Role> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(Role::getRoleId,collect);
            List<Role> roles = roleMapper.selectList(lambdaQueryWrapper);
            return roles;
        }else {
            return null;
        }
    }
}
