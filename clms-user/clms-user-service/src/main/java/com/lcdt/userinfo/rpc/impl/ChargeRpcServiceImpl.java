package com.lcdt.userinfo.rpc.impl;

import com.lcdt.pay.abc.dto.ChangeAccountBook;
import com.lcdt.pay.abc.service.AbcApiService;
import com.lcdt.userinfo.dao.ChargeRecordMapper;
import com.lcdt.userinfo.model.ChargeRecord;
import com.lcdt.userinfo.rpc.ChargeRpcService;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;

import static com.lcdt.traffic.vo.ConstantVO.FLAG_0000;

/**
 * <AUTHOR>
 * @date 2022-04-06
 */
@Service
@Slf4j
public class ChargeRpcServiceImpl implements ChargeRpcService {


    @Autowired
    private ChargeRecordMapper chargeRecordMapper;

    @Autowired
    private AbcApiService abcApiService;

    @Override
    public void chargeHandle(String payerAbNo, String payeeAbNo, BigDecimal amt, <PERSON>D<PERSON><PERSON><PERSON> chargeFee, Long operatorId, String operator, String affiliatedPlatform) {
        try {
            log.info("将提现手续费划拨到固定账簿{}上", payeeAbNo);
            ChangeAccountBook changeAccountBook = abcApiService.changeAccountBookBalance(payerAbNo, payeeAbNo,
                    String.format("%.2f", chargeFee.divide(new BigDecimal(100))), affiliatedPlatform);
            if (changeAccountBook.getCode().equals(FLAG_0000)) {
                log.info("保存提现手续费记录:{}", changeAccountBook.getJrnNo());
                // 保存提现手续费记录
                ChargeRecord chargeRecord = new ChargeRecord();
                // 暂时固定为2元
                chargeRecord.setChargeFee(chargeFee);
                chargeRecord.setWithdrawalAmount(amt);
                chargeRecord.setOperatorId(operatorId);
                chargeRecord.setOperatorName(operator);
                chargeRecord.setOrderNo(changeAccountBook.getJrnNo());
                chargeRecord.setCreateTime(new Date());
                chargeRecordMapper.insert(chargeRecord);
            }
        } catch (Exception e) {
            log.error("司机提现error", e);
        }
    }
}
