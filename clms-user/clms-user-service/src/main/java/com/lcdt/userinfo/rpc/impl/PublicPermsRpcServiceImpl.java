package com.lcdt.userinfo.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lcdt.security.exception.GerenicRunException;
import com.lcdt.userinfo.dao.PublicPermsMapper;
import com.lcdt.userinfo.model.PublicPerms;
import com.lcdt.userinfo.rpc.PublicPermsRpcService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class PublicPermsRpcServiceImpl implements PublicPermsRpcService {

    @Autowired
    private PublicPermsMapper publicPermsMapper;

    /**
     * 分页查询列表
     *
     * @param page
     * @param publicPerms
     * @return
     */
    @Override
    public IPage<PublicPerms> queryPageList(Page<PublicPerms> page, PublicPerms publicPerms) {
        return publicPermsMapper.selectMyPage(page, publicPerms);
    }

    @Override
    public List<String> queryAllPerms() {
        return publicPermsMapper.selectAllPerms();
    }

    /**
     * 新增权限
     *
     * @param publicPerms
     * @return
     */
    @Override
    public int add(PublicPerms publicPerms) {
        // 去除路径权限可能存在的空格问题
        publicPerms.setPerms(publicPerms.getPerms().trim());
        // 先判断是否路径权限是有已经存在
        Long count = publicPermsMapper.selectCount(new QueryWrapper<PublicPerms>().lambda()
                .eq(PublicPerms::getPerms, publicPerms.getPerms()));
        if (count > 0) {
            throw new GerenicRunException("该路公共路径权限已经存在");
        }
        return publicPermsMapper.insert(publicPerms);
    }

    /**
     * 移除权限
     *
     * @param ppId
     * @return
     */
    @Override
    public int remove(Long ppId) {
        return publicPermsMapper.deleteById(ppId);
    }

    @Override
    public int modify(PublicPerms publicPerms) {
        return publicPermsMapper.updateById(publicPerms);
    }
}
