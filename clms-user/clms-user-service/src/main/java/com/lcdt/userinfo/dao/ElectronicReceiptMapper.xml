<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.ElectronicReceiptMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.ElectronicReceipt">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="er_id" jdbcType="BIGINT" property="erId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo"/>
        <result column="receipt_no" jdbcType="VARCHAR" property="receiptNo"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="cdn_url" jdbcType="VARCHAR" property="cdnUrl"/>
        <result column="oss_url" jdbcType="VARCHAR" property="ossUrl"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        er_id, order_no, out_trade_no, receipt_no, status, cdn_url, oss_url, create_time
    </sql>

</mapper>