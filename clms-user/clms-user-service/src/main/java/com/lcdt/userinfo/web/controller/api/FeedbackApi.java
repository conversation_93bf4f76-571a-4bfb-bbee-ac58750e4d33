package com.lcdt.userinfo.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.exception.GerenicRunException;
import com.lcdt.userinfo.dto.FeedbackDto;
import com.lcdt.userinfo.dto.FeedbackParamsDto;
import com.lcdt.userinfo.model.Feedback;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.rpc.FeedbackRpcService;
import com.lcdt.userinfo.web.dto.PageBaseDto;
import com.lcdt.util.ResponseJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
/**
 * 投诉管理接口
 */
@RequestMapping("/api/feedback")
public class FeedbackApi {

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private FeedbackRpcService feedbackRpcService;

    /**
     * 司机投诉列表
     *
     * @param feedbackParamsDto 查询参数
     * @return 投诉列表
     */
    @GetMapping("/dlist")
    public PageBaseDto driverFbList(FeedbackParamsDto feedbackParamsDto) {
        if (!ObjectUtils.isEmpty(feedbackParamsDto.getCompanyName())) {
            throw new GerenicRunException("非法请求");
        }
        feedbackParamsDto.setType(0);
        PageInfo<Feedback> feedbackPageInfo = feedbackRpcService.queryFeedbackManageList(feedbackParamsDto);
        PageBaseDto<Feedback> dto = new PageBaseDto<>();
        dto.setList(feedbackPageInfo.getList());
        dto.setTotal(feedbackPageInfo.getTotal());
        return dto;
    }


    /**
     * 托运人投诉列表
     *
     * @param feedbackParamsDto 查询参数
     * @return 投诉列表
     */
    @GetMapping("/slist")
    public JSONObject shipperFbList(FeedbackParamsDto feedbackParamsDto) {
        if (!ObjectUtils.isEmpty(feedbackParamsDto.getDriverName())) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("非法请求！");
        }
        feedbackParamsDto.setType(1);
        PageInfo<Feedback> feedbackPageInfo = feedbackRpcService.queryFeedbackManageList(feedbackParamsDto);
        return ResponseJsonUtils.successResponseJson(feedbackPageInfo, "请求成功");
    }


    /**
     * 投诉处理
     *
     * @param feedbackDto 反馈信息
     * @return 处理结果
     */
    @PostMapping("/reply")
    public JSONObject fbReply(FeedbackDto feedbackDto) {
        if (ObjectUtils.isEmpty(feedbackDto.getFbResult())) {
            return ResponseJsonUtils.failedResponseJsonWithoutData("请输入处理结果！");
        }
        User user = securityInfoGetter.getUserInfo();
        int i = feedbackRpcService.sbReply(feedbackDto, user.getUserId());
        if (i == 0) {
            return ResponseJsonUtils.failedResponseJsonWithoutData(-1, "处理失败！");
        }
        return ResponseJsonUtils.successResponseJson("处理成功");
    }
}

