<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.DriverLoginLogMapper">

    <select id="selectLastByDriverPhone" resultType="com.lcdt.userinfo.model.DriverLoginLog">
        SELECT ll.*
        FROM driver_login_log ll
        LEFT JOIN tr_driver td ON ll.driver_id = td.driver_id
        <where>
            td.driver_phone = #{driverPhone}
        </where>
        ORDER BY ll.log_id DESC
        LIMIT 1
    </select>
</mapper>
