package com.lcdt.userinfo.service.impl;

import com.github.pagehelper.PageHelper;
import com.lcdt.userinfo.dao.ReconciliationRecordMapper;
import com.lcdt.userinfo.model.ReconciliationRecord;
import com.lcdt.userinfo.service.ReconciliationRecordService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class ReconciliationRecordServiceImpl implements ReconciliationRecordService {

    @Autowired
    private ReconciliationRecordMapper reconciliationRecordMapper;

    @Override
    public List<ReconciliationRecord> queryPageList(ReconciliationRecord reconciliationRecord) {
        PageHelper.startPage(reconciliationRecord.getPageNo(), reconciliationRecord.getPageSize());
        List<ReconciliationRecord> list = reconciliationRecordMapper.selectPageList(reconciliationRecord);
        return list;
    }
}
