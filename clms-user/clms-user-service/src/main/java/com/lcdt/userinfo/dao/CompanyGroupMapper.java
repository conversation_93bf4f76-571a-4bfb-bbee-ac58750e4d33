package com.lcdt.userinfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lcdt.userinfo.dto.CompGroupQueryDto;
import com.lcdt.userinfo.model.CompanyGroup;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
*  <AUTHOR>
*/
public interface CompanyGroupMapper extends BaseMapper<CompanyGroup> {

    /**
     * 用户组列表
     * @param map
     * @return
     */
    List<CompanyGroup> groupCompanyList(Map map);



    /**
     * 企业组列表
     * @param dto
     * @return
     */
    List<CompanyGroup> companyGroupList(CompGroupQueryDto dto);


    /***
     * 权限企业组
     * @param companyId
     * @param groupIds
     * @return
     */
    List<CompanyGroup> companyGroupListEx(@Param(value = "companyId") Long companyId, @Param(value="groupIds") String groupIds);

    List<CompanyGroup> userGroupListEx(@Param(value = "userId") Long userId,  @Param(value="groupIds") String groupIds);

}