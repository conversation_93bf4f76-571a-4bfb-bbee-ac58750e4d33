package com.lcdt.userinfo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.lcdt.userinfo.dao.CompanyThirdMapper;
import com.lcdt.userinfo.model.CompanyThird;
import com.lcdt.userinfo.service.CompanyThirdService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-30
 */
@Service
public class CompanyThirdServiceImpl implements CompanyThirdService {

    @Autowired
    private CompanyThirdMapper companyThirdMapper;

    @Override
    public int saveTjswInfo(CompanyThird companyThird) {
        int rows = companyThirdMapper.update(companyThird, new UpdateWrapper<CompanyThird>().lambda()
                .eq(CompanyThird::getCompId, companyThird.getCompId()));
        if (rows == 0) {
            rows = companyThirdMapper.insert(companyThird);
        }
        return rows;
    }

    @Override
    public CompanyThird queryTjswUuid(Long compId) {
        List<CompanyThird> companyThirdList = companyThirdMapper.selectList(new QueryWrapper<CompanyThird>().lambda()
                .select(CompanyThird::getTjswDduuid)
                .eq(CompanyThird::getCompId, compId));
        return CollectionUtil.isNotEmpty(companyThirdList) ? companyThirdList.get(0) : null;
    }
}
