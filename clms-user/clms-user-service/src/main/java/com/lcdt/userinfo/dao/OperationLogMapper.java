package com.lcdt.userinfo.dao;

import com.lcdt.userinfo.model.OperationLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface OperationLogMapper {
    int deleteByPrimaryKey(Long logId);

    int insert(OperationLog record);

    OperationLog selectByPrimaryKey(Long logId);

    List<OperationLog> selectAll();

    int updateByPrimaryKey(OperationLog record);

    List<OperationLog> selectOperationList(@Param("companyId") Long companyId, @Param("userName") String userName , @Param("userId") Long userId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);
}