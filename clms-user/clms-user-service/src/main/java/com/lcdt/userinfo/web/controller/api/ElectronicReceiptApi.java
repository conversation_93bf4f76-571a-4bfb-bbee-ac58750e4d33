package com.lcdt.userinfo.web.controller.api;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.common.prop.AbcProperties;
import com.lcdt.traffic.service.PayeeAccountService;
import com.lcdt.userinfo.model.BalanceRecord;
import com.lcdt.userinfo.model.CarrierBalanceRecord;
import com.lcdt.userinfo.model.ElectronicReceipt;
import com.lcdt.userinfo.rpc.ElectronicReceiptRpcService;
import com.lcdt.userinfo.service.BalanceRecordService;
import com.lcdt.userinfo.service.CarrierBalanceService;
import com.lcdt.util.ResponseJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
/**
 * 电子业务凭证接口
 */
@RestController
@RequestMapping("/electronicreceipt")
public class ElectronicReceiptApi {

    @Autowired
    private ElectronicReceiptRpcService electronicReceiptRpcService;

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @Autowired
    private PayeeAccountService payeeAccountService;

    @Autowired
    private CarrierBalanceService carrierBalanceService;

    @Autowired
    private BalanceRecordService balanceRecordService;

    @Autowired
    private AbcProperties abcProperties;


    @GetMapping("/query")
    /**
     * 查询对应的电子凭证信息
     *
     * @param outTradeNo 交易号
     * @return 电子凭证信息
     */
    public JSONObject electronicReceiptInfo(String outTradeNo) {
        ElectronicReceipt electronicReceipt = electronicReceiptRpcService.queryByOutTradeNo(outTradeNo);
        return ResponseJsonUtils.successResponseJson(electronicReceipt);
    }

    @Deprecated
    @GetMapping("/carrier/apply")
    /**
     * 承运人端打款给司机或车队长-申请电子凭证信息
     *
     * @param outTradeNo 交易号
     * @param companyId 公司ID
     * @return 申请结果
     */
    public JSONObject electronicReceiptApplyCarrier(String outTradeNo, Long companyId) {
        CarrierBalanceRecord carrierBalanceRecord = carrierBalanceService.queryByOutTradeNo(outTradeNo);
        Integer type = carrierBalanceRecord.getBankStatementType();
        String amount = "";
        String payerAccount, payeeAccount = "";
        if (type == 1 || type == 3) {
            // 1 - 运费收入，托运人支付运费到平台结算户 3 - 充值
            amount = String.format("%.2f", carrierBalanceRecord.getBankStatementCurrentAmount()
                    .subtract(carrierBalanceRecord.getBankStatementAmount())
                    .divide(new BigDecimal(100)));
            payerAccount = abcProperties.getAccountNo();
            payeeAccount = abcProperties.getSettlementAccountNo();
        } else {
            amount = String.format("%.2f", carrierBalanceRecord.getBankStatementAmount()
                    .subtract(carrierBalanceRecord.getBankStatementCurrentAmount())
                    .divide(new BigDecimal(100)));
            payerAccount = abcProperties.getSettlementAccountNo();
            payeeAccount = abcProperties.getAccountNo();
        }
        try {
            electronicReceiptRpcService.abcReceiptApply(payerAccount, payeeAccount, amount,
                    DateUtil.format(carrierBalanceRecord.getRecordTime(), "yyyyMMdd"),
                    carrierBalanceRecord.getRelateOrderNo(),
                    carrierBalanceRecord.getOutTradeNo());
            return ResponseJsonUtils.successResponseJson("申请成功，请稍后重新查看电子凭证");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseJsonUtils.failedResponseJson("申请失败");
        }
    }


    @GetMapping("/shipper/apply")
    /**
     * 托与人端打款到平台-申请电子凭证信息
     *
     * @param outTradeNo 交易号
     * @return 申请结果
     */
    public JSONObject electronicReceiptApplyShipper(String outTradeNo) {
        try {
            BalanceRecord balanceRecord = balanceRecordService.queryByOutTradeNo(outTradeNo);
            String amount = String.format("%.2f", balanceRecord.getChangeAmount().divide(new BigDecimal(100)));
            electronicReceiptRpcService.abcReceiptApply(
                    abcProperties.getAccountNo(),
                    abcProperties.getSettlementAccountNo(), amount,
                    DateUtil.format(balanceRecord.getCreateTime(), "yyyyMMdd"),
                    balanceRecord.getJrnNo(),
                    balanceRecord.getOutTradeNo());
            return ResponseJsonUtils.successResponseJson("申请成功，请稍后重新查看电子凭证");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseJsonUtils.failedResponseJson("申请失败");
        }
    }

}
