package com.lcdt.userinfo.rpc.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lcdt.common.prop.AbcProperties;
import com.lcdt.pay.abc.dto.AbcFile;
import com.lcdt.pay.abc.service.AbcApiService;
import com.lcdt.userinfo.dao.BalanceRecordMapper;
import com.lcdt.userinfo.dao.CarrierBalanceRecordMapper;
import com.lcdt.userinfo.dto.JsonParse;
import com.lcdt.userinfo.model.BalanceRecord;
import com.lcdt.userinfo.model.CarrierBalanceRecord;
import com.lcdt.userinfo.rpc.AbcBusinessService;
import com.lcdt.userinfo.rpc.ElectronicReceiptRpcService;
import com.lcdt.util.AbcFileResolver;
import com.lcdt.util.HttpUtil;
import com.lcdt.util.JsonMapper;
import com.lcdt.util.bo.AbcAccountDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-01-31
 */
@Service
public class AbcBusinessServiceImpl implements AbcBusinessService {

    @Autowired(required = false)
    private AbcApiService abcApiService;

    @Value("${abc.oss-upload-url}")
    private String abcOssUploadUrl;

    @Autowired
    private CarrierBalanceRecordMapper carrierBalanceRecordMapper;

    @Autowired
    private BalanceRecordMapper balanceRecordMapper;

    @Autowired
    private ElectronicReceiptRpcService electronicReceiptRpcService;

    @Autowired
    private AbcProperties abcProperties;


    @Override
    public Integer abcJrnNoAndElectronicReceiptApply(String startDate, String endDate) throws Exception {
        // 1.调用账户明细查询接口获取一天的账户交易详情
        AbcFile abcFile = abcApiService.getCarrierAccountBookInfo(startDate, endDate);
        if (ObjectUtil.isNotNull(abcFile.getFileName())) {
            // 等待3s，下载文件需要时间，直接读取可能存在读取超时的问题
            Thread.sleep(3000);
            // 2.拿到文件名，调用前置机服务读取文件内容，解析内容
            HashMap<String, Object> params = new HashMap<>();
            params.put("originName", abcFile.getFileName());
            String msg = HttpUtil.doPost(abcOssUploadUrl + "/oss/uploadLocalFile", params);
            JsonParse jsonParse = JsonMapper.fromJsonString(msg, JsonParse.class);
            List<AbcAccountDetails> accountDetails = AbcFileResolver.getAccountDetails(jsonParse.getData());
            // 遍历更新日志号
            int rows = 0;
            for (AbcAccountDetails aad : accountDetails) {
                // 判断金额正负，负为托运人支付运费，正为充值或运费收入
                int count = carrierBalanceRecordMapper.update(null, new LambdaUpdateWrapper<CarrierBalanceRecord>()
                        .set(CarrierBalanceRecord::getJrnNo, aad.getJrnNo())
                        .eq(CarrierBalanceRecord::getOutTradeNo, aad.getOutTradeNo()));
                double amount = NumberUtil.parseDouble(aad.getAmount());
                if (count > 0 && amount < 0) {
                    rows++;
                    // 申请电子凭证
                    electronicReceiptRpcService.abcReceiptApply(abcProperties.getSettlementAccountNo(),
                            abcProperties.getAccountNo(),
                            Math.abs(amount) + "",
                            aad.getTrDate(), aad.getJrnNo(), aad.getOutTradeNo());
                }
                // 如果是正的，还需要更新托运人端余额记录
                if (amount > 0) {
                    balanceRecordMapper.update(null, new LambdaUpdateWrapper<BalanceRecord>()
                            .set(BalanceRecord::getJrnNo, aad.getJrnNo())
                            .eq(BalanceRecord::getOutTradeNo, aad.getOutTradeNo()));
                }
            }
            return rows;
        }
        return 0;
    }

    @Override
    public Integer abcJrnNoGetAndUpdate(String startDate, String endDate) throws Exception {
        // 1.调用账户明细查询接口获取一天的账户交易详情
        AbcFile abcFile = abcApiService.getCarrierAccountBookInfo(startDate, endDate);
        if (ObjectUtil.isNotNull(abcFile.getFileName())) {
            // 循环判断是文件下载完毕
            int ct = 0;
            boolean b = false;
            while (true) {
                ct++;
                HashMap<String, Object> params = new HashMap<>();
                params.put("originName", abcFile.getFileName());
                String msg = HttpUtil.doPost(abcOssUploadUrl + "/oss/fileExists", params);
                b = Boolean.parseBoolean(msg);
                // 最多执行5次后自动退出
                if (b || ct > 5) {
                    break;
                }
                Thread.sleep(2000);
            }
            if (b) {
                // 等待3s，下载文件需要时间，直接读取可能存在读取超时的问题
                Thread.sleep(3000);
                // 2.拿到文件名，调用前置机服务读取文件内容，解析内容
                HashMap<String, Object> params = new HashMap<>();
                params.put("originName", abcFile.getFileName());
                String msg = HttpUtil.doPost(abcOssUploadUrl + "/oss/uploadLocalFile", params);
                JsonParse jsonParse = JsonMapper.fromJsonString(msg, JsonParse.class);
                List<AbcAccountDetails> accountDetails = AbcFileResolver.getAccountDetails(jsonParse.getData());
                // 遍历更新日志号
                int rows = 0;
                for (AbcAccountDetails aad : accountDetails) {
                    // 判断金额正负，负为托运人支付运费，正为充值或运费收入
                    rows += carrierBalanceRecordMapper.update(null, new LambdaUpdateWrapper<CarrierBalanceRecord>()
                            .set(CarrierBalanceRecord::getJrnNo, aad.getJrnNo())
                            .eq(CarrierBalanceRecord::getOutTradeNo, aad.getOutTradeNo()));
                    // 如果是正的，还需要更新托运人端余额记录
                    if (NumberUtil.parseDouble(aad.getAmount()) > 0) {
                        rows += balanceRecordMapper.update(null, new LambdaUpdateWrapper<BalanceRecord>()
                                .set(BalanceRecord::getJrnNo, aad.getJrnNo())
                                .eq(BalanceRecord::getOutTradeNo, aad.getOutTradeNo()));
                    }
                }
                return rows;
            }
        }
        return 0;
    }

    public static void main(String[] args) throws Exception {
        int ct = 0;
        while (true) {
            ct++;
            HashMap<String, Object> params = new HashMap<>();
            params.put("originName", "ddd.txt");
            String abcOssUploadUrl = "http://localhost:8022";
            String msg = HttpUtil.doPost(abcOssUploadUrl + "/oss/fileExists", params);
            boolean b = Boolean.parseBoolean(msg);
            if (b || ct > 10) {
                break;
            }
            Thread.sleep(2000);
        }
        System.out.println("结束---------");
    }
}
