package com.lcdt.userinfo.rpc.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.userinfo.dao.CompanyGroupMapper;
import com.lcdt.userinfo.dao.GroupMapper;
import com.lcdt.userinfo.dao.UserGroupMapper;
import com.lcdt.userinfo.dto.CompGroupQueryDto;
import com.lcdt.userinfo.dto.GroupQueryDto;
import com.lcdt.userinfo.dto.UserGroupQueryDto;
import com.lcdt.userinfo.model.CompanyGroup;
import com.lcdt.userinfo.model.Group;
import com.lcdt.userinfo.model.UserGroup;
import com.lcdt.userinfo.rpc.GroupRpcService;
import com.lcdt.util.CheckEmptyUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Created by ybq on 2020/9/7 11:48
 */
@Service
@Transactional
public class GroupRpcServiceImpl implements GroupRpcService {


    @Autowired
    GroupMapper groupMapper;

    @Autowired
    UserGroupMapper userGroupMapper;

    @Autowired
    CompanyGroupMapper companyGroupMapper;

    @Override
    public UserGroup addUserToGroup(Long companyId, Long userId, Group group) {
        UserGroup userGroupRelation = new UserGroup();
        userGroupRelation.setCompanyId(companyId);
        userGroupRelation.setUserId(userId);
        userGroupRelation.setGroupId(group.getGroupId());
        userGroupMapper.insert(userGroupRelation);
        return userGroupRelation;
    }

    @Override
    public PageInfo groupList(GroupQueryDto dto) {
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        return new PageInfo(groupMapper.groupList(dto));
    }

    @Override
    public List<Group> groupAllList(GroupQueryDto dto) {
        List<Group> groups = groupMapper.groupList(dto);
        return groups;
    }

    @Override
    public int addGroup(Group group) {
        Long flag = groupMapper.selectCount(new QueryWrapper<Group>().lambda()
                .eq(Group::getGroupName, group.getGroupName())
                .eq(Group::getCompanyId, group.getCompanyId()));
        if (flag > 0) {
            throw new RuntimeException("该业务组已存在");
        }
        return groupMapper.insert(group);
    }

    @Override
    public int modifyGroup(Group group) {
        Long flag = groupMapper.selectCount(new QueryWrapper<Group>().lambda()
                .eq(Group::getGroupName, group.getGroupName())
                .eq(Group::getCompanyId, group.getCompanyId())
                .ne(Group::getGroupId, group.getGroupId()));
        if (flag > 0) {
            throw new RuntimeException("该业务组已存在");
        }
        return groupMapper.updateById(group);
    }

    @Override
    public int startStop(Group group) {
        if (group.getStatus().equals(0)) { //如果禁用，检查关系表中是否存在用户，存在不允许禁用
//            QueryWrapper<UserGroup> ug = new QueryWrapper<>();
//            ug.eq("group_id", group.getGroupId());
//            ug.eq("company_id", group.getCompanyId());
            if (userGroupMapper.selectCount(Wrappers.<UserGroup>lambdaQuery().eq(UserGroup::getGroupId, group.getGroupId()).eq(UserGroup::getCompanyId, group.getCompanyId())) > 0) {
                throw new RuntimeException("该业务部下存在员工，不可停用！");
            }
        }
        return groupMapper.update(group, new QueryWrapper<Group>().lambda()
                .eq(Group::getCompanyId, group.getCompanyId())
                .eq(Group::getGroupId, group.getGroupId()));
    }

    @Override
    public PageInfo groupUserList(UserGroupQueryDto dto) {
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        return new PageInfo(userGroupMapper.userGroupList(dto));
    }

    @Override
    public int addUserGroup(UserGroup userGroup) {
//        QueryWrapper<UserGroup> ug = new QueryWrapper<>();
//        ug.eq("user_id", userGroup.getUserId());
//        ug.eq("group_id", userGroup.getGroupId());
//        ug.eq("company_id", userGroup.getCompanyId());
        if (userGroupMapper.selectCount(Wrappers.<UserGroup>lambdaQuery().eq(UserGroup::getUserId, userGroup.getUserId()).eq(UserGroup::getGroupId, userGroup.getGroupId()).eq(UserGroup::getCompanyId, userGroup.getCompanyId())) > 0) {
            throw new RuntimeException("该业务组已存在此用户");
        }
        return userGroupMapper.insert(userGroup);
    }

    @Override
    public int removeUserGroup(UserGroup userGroup) {
        return userGroupMapper.delete(Wrappers.<UserGroup>lambdaQuery().eq(UserGroup::getUserId, userGroup.getUserId()).eq(UserGroup::getGroupId, userGroup.getGroupId()).eq(UserGroup::getCompanyId, userGroup.getCompanyId()));
    }

    @Override
    public PageInfo groupCmpList(CompGroupQueryDto dto) {
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        return new PageInfo(companyGroupMapper.companyGroupList(dto));
    }

    @Override
    public int addCompanyGroup(CompanyGroup companyGroup) {
//        QueryWrapper<CompanyGroup> ug = new QueryWrapper<>();
//        ug.eq("group_id", companyGroup.getGroupId());
//        ug.eq("cmp_id", companyGroup.getCmpId()); // 前端传来的要加入的企业ID
//        ug.eq("company_id", companyGroup.getCompanyId());
        if (companyGroupMapper.selectCount(Wrappers.<CompanyGroup>lambdaQuery().eq(CompanyGroup::getGroupId, companyGroup.getGroupId()).eq(CompanyGroup::getCmpId, companyGroup.getCmpId()).eq(CompanyGroup::getCompanyId, companyGroup.getCompanyId())) > 0) {
            throw new RuntimeException("该业务组已存在此用户");
        }
        return companyGroupMapper.insert(companyGroup);
    }

    @Override
    public int removeCompanyGroup(CompanyGroup companyGroup) {
//        QueryWrapper<CompanyGroup> ug = new QueryWrapper<>();
//        ug.eq("group_id", companyGroup.getGroupId());
//        ug.eq("cmp_id", companyGroup.getCmpId()); // 前端传来的要加入的企业ID
//        ug.eq("company_id", companyGroup.getCompanyId());
        return companyGroupMapper.delete(Wrappers.<CompanyGroup>lambdaQuery().eq(CompanyGroup::getGroupId, companyGroup.getGroupId()).eq(CompanyGroup::getCmpId, companyGroup.getCmpId()).eq(CompanyGroup::getCompanyId, companyGroup.getCompanyId()));
    }

    @Override
    public List<UserGroup> userGroupList(Long userId, Long companyId) {
        return userGroupMapper.userGroup4Start(userId, companyId); //这块采用启用逻辑
    }

    @Override
    public List<CompanyGroup> companyGroupList(Long companyId, String groupIds) {
        return companyGroupMapper.companyGroupListEx(companyId, groupIds);
    }

    @Override
    public String companyGroups(Long companyId, String groupIds) {
        List<CompanyGroup> companyGroups = companyGroupList(companyId, groupIds);
        if (!CollectionUtils.isEmpty(companyGroups)) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < companyGroups.size(); i++) {
                sb.append(companyGroups.get(i).getCmpId()).append(",");
            }
            return sb.toString().substring(0, sb.toString().length() - 1);
        }
        return null;
    }

    @Override
    public String userGroups(Long userId, String groupIds) {
        List<CompanyGroup> companyGroups =  companyGroupMapper.userGroupListEx(userId, groupIds);
        if (CheckEmptyUtil.isNotEmpty(companyGroups)) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < companyGroups.size(); i++) {
                if(CheckEmptyUtil.isNotEmpty(companyGroups.get(i))){
                    sb.append(companyGroups.get(i).getCmpId()).append(",");
                }
            }
            if(CheckEmptyUtil.isNotEmpty(sb)){
                return sb.toString().substring(0, sb.toString().length() - 1);
            }else {
                return null;
            }
        }
        return null;
    }

    @Override
    public List<Group> groupList4UserId(Long userId, Long companyId) {
        return userGroupMapper.groupList4UserId(userId, companyId, null);
    }

    @Override
    public List<Group> groupList4UserId(Long userId, Long companyId, Integer status) {
        return userGroupMapper.groupList4UserId(userId, companyId, status);
    }

    @Override
    public Group groupInfo(Long groupId, Long companyId) {
        return groupMapper.selectOne(Wrappers.<Group>lambdaQuery().eq(Group::getGroupId, groupId).eq(Group::getCompanyId, companyId));
    }

    @Override
    public Group groupInfo(Long groupId) {
        return groupMapper.selectById(groupId);
    }


}
