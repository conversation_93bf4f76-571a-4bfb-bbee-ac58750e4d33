<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.BalanceRecordMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.BalanceRecord">
        <id column="br_id" jdbcType="BIGINT" property="brId"/>
        <result column="change_amount" jdbcType="DECIMAL" property="changeAmount"/>
        <result column="final_amount" jdbcType="DECIMAL" property="finalAmount"/>
        <result column="change_type" jdbcType="SMALLINT" property="changeType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="cloud_tag" jdbcType="SMALLINT" property="cloudTag"/>
        <result column="relate_order_no" jdbcType="VARCHAR" property="relateOrderNo"/>
        <result column="merchant_id" jdbcType="VARCHAR" property="merchantId"/>
        <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo"/>
        <result column="jrn_no" jdbcType="VARCHAR" property="jrnNo"/>

    </resultMap>
    <sql id="Base_Column_List">
    br_id, change_amount, final_amount, change_type, remark, create_time, operator, pay_type, merchant_id,relate_order_no,out_trade_no
  </sql>
    <select id="selectByCondition" parameterType="com.lcdt.userinfo.dto.BalanceRecordDto"
            resultType="com.lcdt.userinfo.model.BalanceRecord">
        select
        t.br_id,
        t.change_amount,
        t.final_amount,
        t.change_type,
        t.remark,
        t.create_time,
        t.operator,
        t.pay_type,
        t.merchant_id,
        t.relate_order_no,
        t.out_trade_no,
        t.jrn_no,
        o.record_remark AS "settlementNo"
        from uc_balance_record t
        left join uc_carrier_balance_record o on t.relate_order_no = o.relate_order_no and o.bank_statement_type = '1'
        <where>
            <if test="br.changeType!=null">
                and t.change_type = #{br.changeType}
            </if>
            <if test="br.createTime1!=null">
                and t.create_time &gt; #{br.createTime1}
            </if>
            <if test="br.createTime2!=null">
                and t.create_time &lt; #{br.createTime2}
            </if>
            <if test="br.operator!=null and br.operator!=''">
                and t.operator like concat('%',#{br.operator},'%')
            </if>
            <if test="br.relateOrderNo!=null and br.relateOrderNo!=''">
                and t.relate_order_no like concat('%',#{br.relateOrderNo},'%')
            </if>
            <if test="br.settlementNo!=null and br.settlementNo!=''">
                and o.record_remark = #{br.settlementNo}
            </if>
            <if test="br.companyId!=null">
                and t.company_id = #{br.companyId}
            </if>
        </where>
        order by t.create_time desc
    </select>

    <select id="selectConutByOrderNo" resultType="int">
        select count(1) from uc_balance_record where relate_order_no = #{orderNo}
    </select>
    <select id="selectExportCount" resultType="java.lang.Integer"
            parameterType="com.lcdt.userinfo.dto.BalanceRecordDto">
        select count(1) from (
        select
        t.br_id,
        t.change_amount,
        t.final_amount,
        t.change_type,
        t.remark,
        t.create_time,
        t.operator,
        t.pay_type,
        t.merchant_id,
        t.relate_order_no,
        t.out_trade_no,
        o.record_remark AS "settlementNo"
        from uc_balance_record t
        left join uc_carrier_balance_record o on t.relate_order_no = o.relate_order_no and o.bank_statement_type = '1'
        <where>
            <if test="br.changeType!=null">
                and t.change_type = #{br.changeType}
            </if>
            <if test="br.createTime1!=null">
                and t.create_time &gt; #{br.createTime1}
            </if>
            <if test="br.createTime2!=null">
                and t.create_time &lt; #{br.createTime2}
            </if>
            <if test="br.operator!=null and br.operator!=''">
                and t.operator like concat('%',#{br.operator},'%')
            </if>
            <if test="br.settlementNo!=null and br.settlementNo!=''">
                and o.record_remark =  #{br.settlementNo}
            </if>
            <if test="br.relateOrderNo!=null and br.relateOrderNo!=''">
                and t.relate_order_no like concat('%',#{br.relateOrderNo},'%')
            </if>
            <if test="br.companyId!=null"></if>
        </where>
        and t.company_id = #{br.companyId}
        )count
    </select>
    <select id="selectExportDataLimit" resultType="com.lcdt.userinfo.model.BalanceRecord"
            parameterType="com.lcdt.userinfo.dto.BalanceRecordDto">
        select
        t.br_id,
        t.change_amount,
        t.final_amount,
        t.change_type,
        t.remark,
        t.create_time,
        t.operator,
        t.pay_type,
        t.merchant_id,
        t.relate_order_no,
        t.out_trade_no,
        o.record_remark AS "settlementNo"
        from uc_balance_record t
        left join uc_carrier_balance_record o on t.relate_order_no = o.relate_order_no and o.bank_statement_type = '1'
        <where>
            <if test="br.changeType!=null">
                and t.change_type = #{br.changeType}
            </if>
            <if test="br.createTime1!=null">
                and t.create_time &gt; #{br.createTime1}
            </if>
            <if test="br.createTime2!=null">
                and t.create_time &lt; #{br.createTime2}
            </if>
            <if test="br.operator!=null and br.operator!=''">
                and t.operator like concat('%',#{br.operator},'%')
            </if>
            <if test="br.relateOrderNo!=null and br.relateOrderNo!=''">
                and t.relate_order_no like concat('%',#{br.relateOrderNo},'%')
            </if>
            <if test="br.settlementNo!=null and br.settlementNo!=''">
                and o.record_remark =   #{br.settlementNo}
            </if>
            <if test="br.companyId!=null">
                and t.company_id = #{br.companyId}
            </if>
        </where>
        order by t.create_time desc
        <if test='br.startLimit != null and br.endLimit != null'>
            limit #{br.startLimit},#{br.endLimit}
        </if>
    </select>
</mapper>