<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lcdt.userinfo.dao.CompanyCertificateMapper">
    <resultMap id="BaseResultMap" type="com.lcdt.userinfo.model.CompanyCertificate">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="certi_id" jdbcType="BIGINT" property="certiId"/>
        <result column="comp_id" jdbcType="BIGINT" property="compId"/>
        <result column="identity_front" jdbcType="VARCHAR" property="identityFront"/>
        <result column="identity_back" jdbcType="VARCHAR" property="identityBack"/>
        <result column="three_certificates" jdbcType="VARCHAR" property="threeCertificates"/>
        <result column="yingyezhizhao" jdbcType="VARCHAR" property="yingyezhizhao"/>
        <result column="zuzhijigou" jdbcType="VARCHAR" property="zuzhijigou"/>
        <result column="shuiwudengji" jdbcType="VARCHAR" property="shuiwudengji"/>
        <result column="yinghangkaihu" jdbcType="VARCHAR" property="yinghangkaihu"/>
        <result column="yingyunxuke" jdbcType="VARCHAR" property="yingyunxuke"/>
        <result column="social_credit_code" jdbcType="VARCHAR" property="socialCreditCode"/>
        <result column="road_transport_code" jdbcType="VARCHAR" property="roadTransportCode"/>
        <result column="id_code" jdbcType="VARCHAR" property="idCode"/>
        <result column="cert_type" jdbcType="VARCHAR" property="certType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="identity_name" jdbcType="VARCHAR" property="identityName"/>
        <result column="identity_phone" jdbcType="VARCHAR" property="identityPhone"/>
        <result column="company_type" jdbcType="VARCHAR" property="companyType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="identity_front_id" jdbcType="VARCHAR" property="identityFrontId"/>
        <result column="identity_back_id" jdbcType="VARCHAR" property="identityBackId"/>
        <result column="yingyezhizhao_id" jdbcType="VARCHAR" property="yingyezhizhaoId"/>
        <result column="industry_license_photo" jdbcType="VARCHAR" property="industryLicensePhoto"/>
        <result column="industry_license_photo_id" jdbcType="VARCHAR" property="industryLicensePhotoId"/>
        <result column="alipay_no" jdbcType="VARCHAR" property="alipayNo"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="out_merchant_id" jdbcType="VARCHAR" property="outMerchantId"/>
    </resultMap>

    <select id="selectByCompanyId" resultMap="BaseResultMap">
        select certi_id,
               comp_id,
               identity_front,
               identity_back,
               three_certificates,
               yingyezhizhao,
               zuzhijigou,
               shuiwudengji,
               yinghangkaihu,
               yingyunxuke,
               social_credit_code,
               road_transport_code,
               id_code,
               cert_type,
               remark,
               identity_name,
               identity_phone,
               company_type,
               identity_front_id,
               identity_back_id,
               yingyezhizhao_id,
               industry_license_photo,
               industry_license_photo_id,
               alipay_no,
               order_no,
               out_merchant_id
        from uc_company_certificate
        WHERE comp_id = #{companyId,jdbcType=BIGINT}
    </select>
    <select id="selectShipperList4AccountMange" resultType="com.lcdt.userinfo.dto.CompanyCertDto">
        SELECT t0.comp_id,
        t0.full_name,
        t1.social_credit_code,
        t1.identity_name,
        t1.identity_phone,
        t1.id_code,
        t0.enter_status,
        t0.enter_remark,
        t0.open_pay,
        t0.open_remark,
        t0.ab_no
        FROM uc_company t0
        LEFT JOIN uc_company_certificate t1 ON t0.comp_id = t1.comp_id
        <where>
            and t0.company_type = 1
            <if test="fullName!=null and fullName!=''">
                and t0.full_name like concat('%',#{fullName},'%') or t1.social_credit_code like
                concat('%',#{fullName},'%')
            </if>
            <if test="enterStatus!=null">
                and t0.enter_status = #{enterStatus}
            </if>
        </where>
        order by t0.comp_id desc
    </select>
</mapper>