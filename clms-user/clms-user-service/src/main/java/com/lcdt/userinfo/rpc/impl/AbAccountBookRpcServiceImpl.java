package com.lcdt.userinfo.rpc.impl;

import com.lcdt.userinfo.model.AbAccountBook;
import com.lcdt.userinfo.rpc.AbAccountBookRpcService;
import com.lcdt.userinfo.service.AbAccountBookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/3/21 11:56
 */
@Service
public class AbAccountBookRpcServiceImpl implements AbAccountBookRpcService {

    @Autowired
    private AbAccountBookService abAccountBookService;

    @Override
    public String generateAbNo() {
        return abAccountBookService.generateAbNo();
    }

    @Override
    public int insertAccountBook(AbAccountBook abAccountBook) {
        return abAccountBookService.insertAccountBook(abAccountBook);
    }
}
