package com.lcdt.userinfo.web.controller.api;

import com.lcdt.userinfo.model.ReconciliationRecord;
import com.lcdt.userinfo.service.ReconciliationRecordService;
import com.lcdt.userinfo.web.dto.PageResultDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 对账记录接口
 */
@RestController
@RequestMapping("/api/reconciliationRecord")
public class ReconciliationRecordApi {

    @Autowired
    private ReconciliationRecordService reconciliationRecordService;

    /**
     * 账户余额流水列表
     *
     * @param reconciliationRecord 查询参数
     * @return 流水列表
     */
    @GetMapping("/list")
    public PageResultDto reconciliationRecordList(ReconciliationRecord reconciliationRecord) {
        List<ReconciliationRecord> reconciliationRecordList = reconciliationRecordService.queryPageList(reconciliationRecord);
        PageResultDto<ReconciliationRecord> balanceRecordPageResultDto = new PageResultDto<>(reconciliationRecordList);
        return balanceRecordPageResultDto;
    }

}
