package com.lcdt.userinfo.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.userinfo.dto.CarrierBalanceRecordDto;
import com.lcdt.userinfo.dto.CarrierBalanceRecordDto1;
import com.lcdt.userinfo.service.CarrierBalanceService;
import com.lcdt.userinfo.utils.JSONResponseUtil;
import com.lcdt.userinfo.utils.ResponseMessage;
import com.lcdt.userinfo.web.dto.PageResultDto;
import com.lcdt.util.ResponseJsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
/**
 * 运营端账户管理相关api
 */
@RestController
@RequestMapping("/carrierbalance")
public class CarrierBalanceRecordController {

    @Autowired
    CarrierBalanceService carrierBalanceService;

    @Autowired
    SecurityInfoGetter securityInfoGetter;

    /**
     * 账户余额流水列表
     *
     * @param carrierBalanceRecordDto 查询参数
     * @return 流水列表
     */
    @GetMapping("/recordlist")
    public PageResultDto balanceRecordList(CarrierBalanceRecordDto carrierBalanceRecordDto) {
        List<CarrierBalanceRecordDto1> carrierBalanceRecords = carrierBalanceService.queryBalanceList(carrierBalanceRecordDto);
        PageResultDto<CarrierBalanceRecordDto1> balanceRecordPageResultDto = new PageResultDto<>(carrierBalanceRecords);
        return balanceRecordPageResultDto;
    }


    /**
     * 余额管理导出
     *
     * @param carrierBalanceRecordDto 查询参数
     * @return 导出结果
     * @throws IOException IO异常
     */
    @PostMapping("/exportData")
    public ResponseMessage shipperExportData(CarrierBalanceRecordDto carrierBalanceRecordDto) throws IOException {
        try {
            carrierBalanceService.exprotData(carrierBalanceRecordDto);
            return JSONResponseUtil.success("导出任务创建完成，请留意消息提醒！");
        } catch (Exception ex) {
            return JSONResponseUtil.failure(ex.getMessage(), -1);
        }
    }


    /**
     * 服务费统计
     *
     * @param carrierBalanceRecordDto 查询参数
     * @return 统计结果
     */
    @GetMapping("/servicelist")
    public PageResultDto servicelist(CarrierBalanceRecordDto carrierBalanceRecordDto) {
        carrierBalanceRecordDto.setServiceType("5");//服务费
        List<CarrierBalanceRecordDto1> carrierBalanceRecords = carrierBalanceService.queryBalanceList(carrierBalanceRecordDto);
        PageResultDto<CarrierBalanceRecordDto1> balanceRecordPageResultDto = new PageResultDto<>(carrierBalanceRecords);
        return balanceRecordPageResultDto;
    }


    /**
     * 服务费合计
     *
     * @param carrierBalanceRecordDto 查询参数
     * @return 合计结果
     */
    @GetMapping("/servicetotal")
    public JSONObject servicetotal(CarrierBalanceRecordDto carrierBalanceRecordDto) {
        carrierBalanceRecordDto.setServiceType("5");//服务费
        return ResponseJsonUtils.successResponseJson(carrierBalanceService.getServiceTotal(carrierBalanceRecordDto));
    }


    /**
     * 服务费导出
     *
     * @param carrierBalanceRecordDto 查询参数
     * @param response HTTP响应
     * @return 导出结果
     * @throws IOException IO异常
     */
    @PostMapping("/serviceExportData")
    public ResponseMessage serviceExportData(CarrierBalanceRecordDto carrierBalanceRecordDto, HttpServletResponse response) throws IOException {
        carrierBalanceRecordDto.setServiceType("5");//服务费
        try {
            carrierBalanceService.serviceExprotData(carrierBalanceRecordDto);
            return JSONResponseUtil.success("导出任务创建完成，请留意消息提醒！");
        } catch (Exception ex) {
            return JSONResponseUtil.failure(ex.getMessage(), -1);
        }
    }


}
