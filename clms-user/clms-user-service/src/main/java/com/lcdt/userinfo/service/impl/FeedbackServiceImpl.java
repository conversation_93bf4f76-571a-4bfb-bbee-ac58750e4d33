package com.lcdt.userinfo.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lcdt.userinfo.dao.CompanyMapper;
import com.lcdt.userinfo.dao.FeedbackMapper;
import com.lcdt.userinfo.dto.FeedbackDto;
import com.lcdt.userinfo.dto.FeedbackParamsDto;
import com.lcdt.userinfo.model.Company;
import com.lcdt.userinfo.model.Feedback;
import com.lcdt.userinfo.service.FeedbackService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

@Service
public class FeedbackServiceImpl implements FeedbackService {
    @Autowired
    FeedbackMapper feedbackMapper;
    @Autowired
    CompanyMapper companyMapper;
    @Override
    public int save(Feedback feedback) {
        if(feedback.getFbCompanyId()!=null)
        {
            Company company = companyMapper.selectById(feedback.getFbCompanyId());
            feedback.setFbCompanyName(company.getFullName());
        }
        feedback.setFbTime(new Date());
        feedback.setFbStatus(false);
        return feedbackMapper.insert(feedback);
    }

    @Override
    public PageInfo<Feedback> feedbackList(FeedbackParamsDto feedbackParamsDto) {
        PageHelper.startPage(feedbackParamsDto.getPageNo(), feedbackParamsDto.getPageSize());
        return new PageInfo(feedbackMapper.selectList(feedbackParamsDto));
    }

    public PageInfo<Feedback> driverFeedbackList(FeedbackParamsDto feedbackParamsDto) {
        PageHelper.startPage(feedbackParamsDto.getPageNo(), feedbackParamsDto.getPageSize());
        return new PageInfo(feedbackMapper.selectDriverlist(feedbackParamsDto));
    }

    public List<Feedback> hzFeedbackList(FeedbackParamsDto feedbackParamsDto) {
        PageHelper.startPage(feedbackParamsDto.getPageNo(), feedbackParamsDto.getPageSize());
        return feedbackMapper.selectHzlist(feedbackParamsDto);
    }

    public int updateFb(FeedbackDto feedbackDto, Long userId){
        if(feedbackDto.getFbId()==null){
            return 0;
        }else{
            Feedback feedback = feedbackMapper.selectByPrimaryKey(feedbackDto.getFbId());
            if(feedback==null)
            {
                return 0;
            }
            else{
                feedback.setFbReplyTime(new Date());
                feedback.setFbStatus(true);
                feedback.setFbResult(feedbackDto.getFbResult());
                feedback.setFbResultUser(userId);
                return feedbackMapper.updateByPrimaryKey(feedback);
            }
        }
    }
}
