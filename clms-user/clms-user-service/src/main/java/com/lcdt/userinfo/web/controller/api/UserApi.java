package com.lcdt.userinfo.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.utils.SecurityUtils;
import com.lcdt.converter.ArrayListResponseWrapper;
import com.lcdt.notify.rpcservice.ValidCodeService;
import com.lcdt.userinfo.dao.UserCompRelMapper;
import com.lcdt.userinfo.exception.UserNotExistException;
import com.lcdt.userinfo.model.Role;
import com.lcdt.userinfo.model.User;
import com.lcdt.userinfo.model.UserCompRel;
import com.lcdt.userinfo.service.OperationLogService;
import com.lcdt.userinfo.service.RoleManageService;
import com.lcdt.userinfo.service.UserService;
import com.lcdt.userinfo.web.dto.ModifyUserDto;
import com.lcdt.userinfo.web.dto.UserInfoDto;
import com.lcdt.userinfo.web.exception.PwdErrorException;
import com.lcdt.util.ResponseJsonUtils;
import com.lcdt.util.validate.ValidateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.List;

/**
 * Created by ss on 2017/10/27.
 */
/**
 * 用户接口
 */
@RestController
@RequestMapping("/api/user")
public class UserApi {

    @Autowired
    UserService userService;

    @Autowired
    private ValidCodeService validCodeService;

    @Autowired
    UserCompRelMapper userCompRelMapper;

    @Autowired
    OperationLogService operationLogService;

    @Autowired
    private RoleManageService roleManageService;


    private Logger logger = LoggerFactory.getLogger(UserApi.class);


    private String validcodeTag = "changephone";

    @Autowired
    private SecurityInfoGetter securityInfoGetter;

    @GetMapping("/hello")
    /**
     * 测试获取登录用户数据
     *
     * @param request HTTP请求
     * @return 测试数据
     */
    public String hello(HttpServletRequest request) {
//        User user = securityInfoGetter.getUserInfo();
//        logger.error("请求的域名{}", request.getServerName());
//        return user.getPwd();
        return "test!!!!!!!!!!!!!";
    }


    /**
     * 手机号是否已注册
     *
     * @param phone 手机号
     * @return 注册状态
     */
    @PostMapping(value = "/isPhoneRegister")
    public JSONObject isPhoneRegister(String phone) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 0);
        try {
            User user = userService.queryByPhone(phone);
            user.setPwd("");
            jsonObject.put("data", user);
        } catch (UserNotExistException e) {
            logger.error("用户不存在", e);
            jsonObject.put("data", false);
        }
        return jsonObject;
    }


    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping(value = "/get", produces = "application/json; charset=UTF-8")
    public JSONObject getUserInfo() {
        User user = securityInfoGetter.getUserInfo();
        user = userService.selectUserByPhone(user.getPhone());
        //将密码设置为空字符串
        user.setPwd("");
        UserInfoDto userInfoDto = new UserInfoDto();
        //查询role
        List<Role> role =  roleManageService.selectByUser(user.getUserId());
        user.setRoleList(role);
        userInfoDto.setUser(user);
        return ResponseJsonUtils.successResponseJson(userInfoDto);
    }


    /**
     * 获取企业信息
     *
     * @return 企业信息
     */
    @GetMapping("/getallinfo")
    public JSONObject userCompInfo() {
        if (ObjectUtils.isEmpty(securityInfoGetter.getCompId())) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("userCompRel", null);
            return ResponseJsonUtils.successResponseJson(jsonObject);
        }
        UserCompRel userCompRel = securityInfoGetter.geUserComp();
        final User user = userService.selectUserByPhone(userCompRel.getUser().getPhone());
        userCompRel.setUser(user);
//        List<Role> userRole = roleService.getUserRole(userCompRel.getUserId(), userCompRel.getCompId());
//        userCompRel.setRoles(userRole);
        userCompRel.getUser().setPwd("");
        return ResponseJsonUtils.successResponseJson(userCompRel);
    }


    /**
     * 获取用户权限
     *
     * @return 用户权限列表
     */
    @GetMapping(value = "/authorities")
    public List<GrantedAuthority> getUserAuthorities() {
        Collection<? extends GrantedAuthority> authorities = SecurityContextHolder.getContext().getAuthentication().getAuthorities();
        return new ArrayListResponseWrapper<>(authorities);
    }


    /**
     * 编辑用户个人信息
     *
     * @param modifyUserDto 修改信息
     * @param result 验证结果
     * @return 修改后的用户信息
     */
    @PostMapping(value = "/modify")
    public User editUserInfo(@Validated ModifyUserDto modifyUserDto, BindingResult result) {

        if (result.hasErrors()) {
            throw new ValidateException(result.getAllErrors().get(0).getDefaultMessage());
        }

        User user = securityInfoGetter.getUserInfo();
        user = userService.selectUserByPhone(user.getPhone());
        //user.setNickName(modifyUserDto.getNickName());
        user.setRealName(modifyUserDto.getName());
        user.setPictureUrl(modifyUserDto.getAvatarUrl());
        if (modifyUserDto.getEmail() != null) {
            user.setEmail(modifyUserDto.getEmail());
        }

        if (modifyUserDto.getBirthDay() != null) {
            user.setBirthday(modifyUserDto.getBirthDay());
        }

        if (modifyUserDto.getIntro() != null) {
            user.setIntroMemo(modifyUserDto.getIntro());
        }

        userService.updateUser(user);
        user.setPwd("");
        return user;
    }

    @PostMapping(value = "/modifypwd")
    /**
     * 修改用户密码
     *
     * @param oldPwd 原密码
     * @param newPwd 新密码
     * @return 修改结果
     */
    public JSONObject modifyPwd(String oldPwd, String newPwd) {
        JSONObject jsonObject = new JSONObject();
        String encodeNewpwd = SecurityUtils.encryptPassword(newPwd);
        User user = securityInfoGetter.getUserInfo();
        User originUser = userService.queryByUserId(user.getUserId());
        if (SecurityUtils.matchesPassword(oldPwd, originUser.getPwd())) {
            originUser.setPwd(encodeNewpwd);
            userService.updateUserWithpwd(originUser);
            jsonObject.put("code", 0);
            jsonObject.put("message", "密码修改成功，需重新登录！");
            operationLogService.addOperationLog("修改密码");
        } else {
            throw new PwdErrorException("密码不正确！");
        }
        return jsonObject;
    }


    /**
     * 更换手机号
     *
     * @param request HTTP请求
     * @param pwd 密码
     * @param newphone 新手机号
     * @param captcha 验证码
     * @param oldPhone 原手机号
     * @param oldCaptcha 原手机号验证码
     * @return 更换结果
     */
    @PostMapping(value = "/changephone")
    @ResponseBody
    public JSONObject changePhone(HttpServletRequest request, String pwd, String newphone, String captcha, String oldPhone ,String oldCaptcha) {
        String msg = "";
        int tag = -1;
        JSONObject jsonObject = new JSONObject();
        if (userService.isPhoneBeenRegister(newphone)) {
            throw new ValidateException("手机号" + newphone + "已注册！");
        }
        User user = securityInfoGetter.getUserInfo(); //获取当前登录账号
        try {
            user = userService.queryByUserId(user.getUserId());
        } catch (UserNotExistException e) {
            throw new ValidateException("获取用户信息出错:" + e.getMessage());
        }
        if (!SecurityUtils.matchesPassword(pwd, user.getPwd())) {
            throw new PwdErrorException("密码不正确！");
        }
        boolean codeCorrect = validCodeService.isCodeCorrect(captcha, validcodeTag, newphone);
        if(!codeCorrect){
            msg = "新手机号验证码错误或已过期，请重新获取！";
        }
        boolean oldCodeCorrect = validCodeService.isCodeCorrect(oldCaptcha, validcodeTag, oldPhone);
        if(!oldCodeCorrect){
            msg = "原手机号验证码错误或已过期，请重新获取！";
        }
        user.setPhone(newphone);
        userService.updateUser(user);
        tag = 0;
        msg = "登录手机号码更换成功，请重新登录！";
        operationLogService.addOperationLog("更换手机号");
        jsonObject.put("code", tag);
        jsonObject.put("message", msg);
        return jsonObject;
    }

    /**
     * 发送验证码
     *
     * @param newphone 新手机号
     * @return 发送结果
     */
    @RequestMapping(value = "/sendchangecaptcha", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject getCaptcha(String newphone) {
        String msg = "";
        int tag = -1;
        validCodeService.sendValidCode(validcodeTag, 60 * 5, newphone);
        tag = 0;
        msg = "发送成功！";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("message", msg);
        jsonObject.put("code", tag);
        return jsonObject;
    }


}
