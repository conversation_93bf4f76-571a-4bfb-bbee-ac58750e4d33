package com.lcdt.userinfo.dao;

import com.lcdt.userinfo.model.UserCompRel;
import com.lcdt.userinfo.web.dto.SearchEmployeeDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface UserCompRelMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table uc_user_comp_rel
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long userCompRelId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table uc_user_comp_rel
     *
     * @mbg.generated
     */
    int insert(UserCompRel record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table uc_user_comp_rel
     *
     * @mbg.generated
     */
    UserCompRel selectByPrimaryKey(Long userCompRelId);

    /**
     * 获取用户关联的企业信息
     * @param userId
     * @return
     */
    List<UserCompRel> selectByUserId(Long userId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table uc_user_comp_rel
     *
     * @mbg.generated
     */
    List<UserCompRel> selectAll();

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table uc_user_comp_rel
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(UserCompRel record);

    /***
     * 根据条件获取用户企业关系列表
     * @param map
     * @return
     */

    List<UserCompRel> selectByCondition(Map map);

    List<UserCompRel> selectByUserIdCompanyId(@Param("userId") Long userId, @Param("companyId") Long companyId);

    /***
     * 获取用户创建的所有企业
     * @param userId
     * @param companyId -- 除了个企业之外的
     * @return
     */
    List<UserCompRel> selectCmpAdminByUserId(@Param("userId") Long userId, @Param("companyId") Long companyId,@Param("bindCmpId") Long bindCmpId);


    List<UserCompRel> selectByCompanyIdDepIds(@Param("companyId") Long companyId,@Param("deptIds") String deptIds);

    String selectUserPhoneByCompanyId(@Param("companyId") Long compId);
}