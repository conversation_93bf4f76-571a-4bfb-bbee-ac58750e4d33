package com.lcdt.userinfo.web.dto;


import lombok.Data;

import jakarta.validation.constraints.Size;

/**
 * Created by ss on 2017/11/6.
 */
@Data
public class ModifyInvoiceDto {

    /** 开票抬头 */
    @Size(max = 50)
    private String invoiceTitle;

    /** 税号 */
    @Size(max = 50)
    private String registrationNo;

    /** 开户银行 */
    @Size(max = 50)
    private String bankName;

    /** 银行账号 */
    @Size(max = 50)
    private String bankNo;

    /** 电话号码 */
    @Size(max = 50)
    public String telNo1;

    /** 注册地址 */
    @Size(max = 50)
    public String registrationAddress;
    /** 税源注册地 */
    @Size(max = 50)
    public String taxRegisterAddress;

    /** 备注 */
    @Size(max = 100)
    public String invoiceRemark;

}