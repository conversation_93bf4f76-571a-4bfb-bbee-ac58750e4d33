package com.lcdt.userinfo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lcdt.userinfo.dto.CompanyCertDto;
import com.lcdt.userinfo.model.CompanyCertificate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CompanyCertificateMapper extends BaseMapper<CompanyCertificate> {

    List<CompanyCertificate> selectByCompanyId(Long companyId);

    IPage<CompanyCertDto> selectShipperList4AccountMange(@Param("iPage") IPage<?> iPage,
                                                         @Param("fullName") String fullName,
                                                         @Param("enterStatus") Integer enterStatus);

}