import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

public class Test {
    public static void main(String[] args) {
        List<String> list = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        list.add("/aaa/bbb");
        list.add("/ccc/ddd");
        list.add("/eee/fff/ddd,/ggg/sss/mmm");
        list.stream().filter(url -> url.indexOf(",") > -1).forEach(url -> list2.addAll(Arrays.asList(url.split(","))));

//        System.out.println(list.stream().anyMatch(url -> "/ggg/sss/mmm".equals(url)));
//
//        boolean sst = Stream.concat(list2.stream(), list.stream().filter(url -> !url.contains(","))).anyMatch(url -> "/ggg/sss/mmm".equals(url));
//        System.out.println(sst);

        Stream.concat(list.stream(), list2.stream()).forEach(System.out::println);
    }
}
