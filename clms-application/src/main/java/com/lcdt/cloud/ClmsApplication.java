package com.lcdt.cloud;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 冷链管理系统单应用启动类
 * <p>
 * 组件扫描配置说明：
 * 1. com.lcdt - 扫描所有基础模块
 * 2. com.lcdt.cloud - 扫描应用模块
 * 3. com.lcdt.traffic - 扫描交通管理模块
 * 4. com.lcdt.security - 扫描安全模块
 * 5. com.lcdt.location - 扫描位置服务模块
 * 6. com.lcdt.notify - 扫描通知服务模块
 * 8. com.lcdt.payment - 扫描支付服务模块
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class},
        scanBasePackages = {
                "com.lcdt.depend",
                "com.lcdt.common",
                "com.lcdt.userinfo",
                "com.lcdt.traffic",
                "com.lcdt.security",
                "com.lcdt.location",
                "com.lcdt.notify",
                "com.lcdt.pay"
        })
@MapperScan("${mybatis-plus.mapperPackage}")
public class ClmsApplication {

    /**
     * 应用程序入口点
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(ClmsApplication.class, args);
    }
}