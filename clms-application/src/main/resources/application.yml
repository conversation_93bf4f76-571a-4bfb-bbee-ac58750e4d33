server:
  port: 8081
  # Spring Boot 3 服务器优化配置
  error:
    include-stacktrace: never
    include-message: always
  # HTTP/2 支持
  http2:
    enabled: true
  # 压缩配置
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
spring:
  profiles:
    active: dev
  application:
    name: clms-application

  # Spring Boot 3 性能优化配置
  main:
    banner-mode: console
    web-application-type: servlet
  # 异步处理配置
  task:
    execution:
      pool:
        core-size: 8
        max-size: 16
        queue-capacity: 100
        keep-alive: 60s
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000ms
      cache-null-values: false

  quartz:
    auto-startup: false
    job-store-type: memory
    properties:
      org:
        quartz:
          scheduler:
            instanceName: ClmsScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.simpl.RAMJobStore
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true


acs: key=LSKJ20210705JKSL


esign:
  notice:
    url: https://api.xingyuan56.com/driver/contract/esignSilentNotice

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  # 多包名使用 例如 com.lcdt.**.mapper,org.xxx.**.mapper
  # 对应的 XML 文件位置
  mapperLocations: com.lcdt.**.dao/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.lcdt.userinfo.model,com.lcdt.traffic.model,com.lcdt.pay.model
  mapperPackage: com.lcdt.**.dao

token:
  header: Authorization
  expireTime: 720
  shipperPcExpireTime: 240
  shipperMoExpireTime: 10080
  carrierExpireTime: 240
  secret: wi/932jo1!2TW23*9ss^

jimi:
  appkey: 8FB345B8693CCD00763839A9A7994B1D
  appsecret: e714ffb1180e4d93933a609923414686
  userid: sddtd
  userpassword: Dtdui5678

zhongjiao:
  userName: d92f2276-1688-4f8d-a6f7-7c6e29566d92
  userPass: ******************************
  privateKey: e0be7e6d-7f07-4b4a-a24d-d586f45fdb9b
  clientId: e8ef53eb-14a2-4b8d-855e-e3d411ffb503
  domainName: https://zhiyunopenapi.95155.com

aliyunmq:
  accessKey: LTAI4Fnv3y2fftz6x86faDq9
  secretKey: ******************************
  nameSrvAddr: http://MQ_INST_1754376740117579_BcO7z2bo.mq-internet-access.mq-internet.aliyuncs.com:80

  notification:
    topic: nfps_notify_test
    groupId: GID_NOTIFY_TEST
    tag: tag_notify_test

  timelineMq:
    topic: nfps_timeline_test
    groupId: GID_TIMELINE_TEST
    tag: tag_timelime_test

pagehelper:
  auto-dialect: true
  close-conn: true
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

chuanglansmsapi:
  account: N2175738
  password: X4CC52265B69ea
  smsApiUrl: http://smssh1.253.com/msg/send/json

wechat:
  appId: wxdfd2cd6a247b95d4
  appSecret: 960e661e9638628028dc7a550eaa6eea


aliyun:
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    accessId: LTAI5tMyVHwR2Hir5UyaxJqS
    accessKey: ******************************
    bucket: lylts
    host: https://lylts.oss-cn-beijing.aliyuncs.com

  sms:
    accessKeyId: LTAI5tMyVHwR2Hir5UyaxJqS
    accessKeySecret: ******************************
    signName: 邻商云南科技
    templateCode: SMS_322205026

# ocr configuration
ocr:
  appKey: *********
  appSecret: apbZtBtbSPv8wdL8IpDz2QI6NuMFo7fX
  appCode: ac4d1dcffcd542cfb6902de7076a9397

abc:
  oss-upload-url: http://************:8022