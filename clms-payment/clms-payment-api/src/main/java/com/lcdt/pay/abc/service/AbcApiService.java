package com.lcdt.pay.abc.service;

import com.lcdt.pay.abc.dto.*;

/**
 * <AUTHOR>
 * @date 2022-05-11
 */
public interface AbcApiService {
    /**
     * 创建多级账簿
     *
     * @param abName 账簿名称
     * @param abNo   账簿编号
     * @return
     * @throws Exception
     */
    AccountBook createAccountBook(String abName, String abNo, String affiliatedPlatform) throws Exception;

    /**
     * 汇兑-对公
     *
     * @return
     * @throws Exception
     */
    Transfer transfer4Public(TransferParam param , String affiliatedPlatform) throws Exception;

    /**
     * 汇兑-对私
     *
     * @return
     * @throws Exception
     */
    Transfer transfer4Private(TransferParam param, String affiliatedPlatform) throws Exception;

    /**
     * 账簿余额调整
     *
     * @param payerAbNo
     * @param payeeAbNo
     * @param amt
     * @param affiliatedPlatform
     * @return
     */
    ChangeAccountBook changeAccountBookBalance(String payerAbNo, String payeeAbNo, String amt, String affiliatedPlatform) throws Exception;

    /**
     * 查看账簿余额
     *
     * @param affiliatedPlatform
     * @param abNo
     * @return
     * @throws Exception
     */
    AccountBook getAccountBookAccount(String affiliatedPlatform, String abNo) throws Exception;

    /**
     * 单账薄明细查询（CQRD02）交易
     *
     * @param abNo
     * @param affiliatedPlatform
     * @return
     * @throws Exception
     */
    AbcFile getAccountBookInfo(String abNo, String startDate, String endDate, String affiliatedPlatform) throws Exception;

    /**
     * 账户明细查询（CQRA18）交易
     *
     * @return
     * @throws Exception
     */
    AbcFile getCarrierAccountBookInfo(String startDate, String endDate) throws Exception;

    /**
     * 查询账户余额
     *
     * @return
     * @throws Exception
     */
    AccountBook checkAccountBalance(String affiliatedPlatform) throws Exception;

    /**
     * 汇兑-提现
     * 借方：运营端监管户   带着账簿号
     * 贷方：1.司机 没有账簿号  带着银行卡号
     * 2.运营端 带着银行卡号
     * <p>
     * <p>
     * 附言：提现
     * 他行标志：根据司机/车队长 银行卡是否他行判断
     * 贷方户名：山东行远物流网络科技有限公司
     * 贷方行号：司机/车队长 银行卡号   农行不填   他行填 联行号(监管户联行号)
     * 借方户名：山东行远物流网络科技有限公司
     * 用途：提现
     * 借方账号：监管户账号
     * 借方多级账簿：司机账簿号/车队长账簿号
     * 贷方账号： 银行卡号
     * 金额：金额
     * <p>
     * 需要的是银行卡号和账簿号
     *
     * @return
     * @throws Exception
     */
    Transfer withdraw(TransferParam param, String affiliatedPlatform) throws Exception;

    /**
     * 个性化联机单笔实时查询 PDF 电子回单文件（CMRA93）
     *
     * @param abcFileParam
     * @return
     */
    AbcFile electronicReceipt(AbcFileParam abcFileParam) throws Exception;

    /**
     * 查询金融交易处理状态
     *
     * @param serialNo
     * @return
     * @throws Exception
     */
    DrawWater queryFinancialTransactionsStatus(String serialNo) throws Exception;
}
