package com.lcdt.pay.bkcloudfunds.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class WithdrawalOfHandlingFeesDto implements Serializable {

    private BigDecimal fee;

    private String affiliatedPlatform;

    private String payPassword;

    private String withdrawalOfHandlingFeesCodes;

    private Integer isAbcBank;

    private String bankCard;

    private String name;

    private String driverOpenBankCode;

    private String phone;


}
