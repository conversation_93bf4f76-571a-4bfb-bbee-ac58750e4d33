package com.lcdt.pay.abc.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022-03-11
 */
@Data
public class Transfer extends AbcBase {

    private static final long serialVersionUID = -5055366876270860536L;

    /**
     * 0 - 标识交易成功
     * 关于交易处理结果的判断： 当应答报文公共头的返回来源（RespSource）为 0，则交易成功；
     * 当非 0 则交易异常（存 在交易失败和因通讯异常状态未知两种可能），对于返回来源（RespSource）
     * 非 0 情况从 客户资金安全考虑建议 ERP 都发起“查询金融交易处理状态（CQRT71）”交易。
     * 当交易落 地（Corp/WaitFlag=1）即表示银行已受理等待柜员人工处理，系统会自动查询柜员最终 的处理结果，
     * ERP 用 CQRT71 也可以查询到最新的处理结果。
     */
    private String respSource;

    /**
     * 日志号 农行交易接口返回
     */
    private String jrnNo;


    /**
     * 交易日期
     */
    private String respDate;

    /**
     * 交易落地
     */
    private String waitFlag;


}
