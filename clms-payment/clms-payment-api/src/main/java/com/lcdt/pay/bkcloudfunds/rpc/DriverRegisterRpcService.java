package com.lcdt.pay.bkcloudfunds.rpc;

import com.alibaba.fastjson2.JSONObject;
import com.lcdt.userinfo.dto.DriverWalletDto;
import com.lcdt.userinfo.model.DriverWallet;

/**
 * 司机入驻
 */
public interface DriverRegisterRpcService {

    /**
     * 商户信息修改
     *
     * @param driverId
     * @param driverWalletDto
     * @return
     */
    DriverWallet updateMerchant(DriverWallet driverWallet, Long driverId, DriverWalletDto driverWalletDto) throws Exception;


    int driverOpenAccount(Long driverId, String authCode)throws Exception;
}
