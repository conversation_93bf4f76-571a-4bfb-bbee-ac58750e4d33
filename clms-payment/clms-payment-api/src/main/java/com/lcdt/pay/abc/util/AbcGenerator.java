package com.lcdt.pay.abc.util;


import java.time.LocalDateTime;

/**
 * 生成农行xxx
 *
 * <AUTHOR>
 */
public class AbcGenerator {
    /**
     * 生成流水号 （30位）
     *
     * @return
     */
    public synchronized static String seqNo() {
        LocalDateTime dateTime = LocalDateTime.now();
        String seq = String.format("%tY", dateTime) +
                String.format("%tm", dateTime) +
                String.format("%td", dateTime) +
                String.format("%tH", dateTime) +
                String.format("%tM", dateTime) +
                String.format("%tS", dateTime) +
                System.currentTimeMillis() +
                exportRandom4();
        return seq.substring(0, 30);
    }

    public static int exportRandom4() {
        return (int) ((Math.random() * 9 + 1) * 1000);
    }

    /**
     * 获取格式化的yyyyMMdd日期字符串
     *
     * @return
     */
    public static String getYyyyMmDd() {
        LocalDateTime dateTime = LocalDateTime.now();
        return String.format("%tY", dateTime) +
                String.format("%tm", dateTime) +
                String.format("%td", dateTime);
    }

    public static void main(String[] args) {
        //  System.out.println(outMerchantId("01"));
//        System.out.println((int) ((Math.random() * 9 + 1) * 100000));
//        System.out.println(outTradeNo());
//        String s = seqNo();
//        System.out.println(s);
//        System.out.println(s.length());
        System.out.println(getYyyyMmDd());

    }
}

