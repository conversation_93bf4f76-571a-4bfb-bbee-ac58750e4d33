package com.lcdt.pay.bkcloudfunds.service;

import com.lcdt.traffic.bk.BatchPayFreightVo;
import com.lcdt.userinfo.model.User;

import java.math.BigDecimal;
import java.util.List;

/**
 * 农行支付
 *
 * <AUTHOR>
 * @date 2022-03-11
 */
public interface AbcPaymentService {

    /**
     * 托运人支付
     *
     * @param payment
     * @param goodsInfo
     * @param checkBillId
     * @param settleCode
     * @param serviceCharge
     * @return
     */
    int shipperPay(BigDecimal payment, String goodsInfo, Long checkBillId, String settleCode, BigDecimal serviceCharge);

    /**
     * 运营端批量支付
     *
     * @param batchPayFreightVoList
     * @param payWay
     * @param compId
     * @param user
     * @return
     */
    List<String> carrierBatchPayment(List<BatchPayFreightVo> batchPayFreightVoList, String payWay, Long compId, User user,String platform);
}
