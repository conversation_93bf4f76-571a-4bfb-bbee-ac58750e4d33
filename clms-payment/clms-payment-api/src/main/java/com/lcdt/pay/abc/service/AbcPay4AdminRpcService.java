package com.lcdt.pay.abc.service;

import com.lcdt.pay.abc.dto.TransferParam;

/**
 * <AUTHOR>
 * @date 2022-11-17
 */
public interface AbcPay4AdminRpcService {


    /**
     * 支付退回 （监管户 -> 结算户）
     *
     * @param transferParam
     * @return
     * @throws Exception
     */
    String payBack(TransferParam transferParam) throws Exception;

    /**
     * 托运人支付退回（走提现操作）
     *
     * @param transferParam
     * @return
     * @throws Exception
     */
    String withdraw(TransferParam transferParam) throws Exception;
}
