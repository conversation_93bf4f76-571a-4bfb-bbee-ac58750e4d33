package com.lcdt.pay.abc.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-03-11
 */
@Data
public class TransferParam implements Serializable {

    private static final long serialVersionUID = -1594914132039961846L;

    /**
     * 请求的reqNo （平台自有字段，非农行字段）
     */
    private String seqNo;

    /**
     * 金额
     */
    private String amount;

    /**
     * 付款人真实姓名（借方）
     */
    private String payerName;
    /**
     * 付款人账号（借方）
     */
    private String payerNo;

    /**
     * 付款方账簿号（借方）
     */
    private String payerAbNo;

    /**
     * 收款人真实姓名（贷方）
     */
    private String payeeName;

    /**
     * 收款人账号（贷方）
     */
    private String payeeNo;

    /**
     * 收款方行号（贷方）
     */
    private String payeeBankNo;

    /**
     * 收款方账簿号（贷方）
     */
    private String payeeAbNo;


    /**
     * 附言
     */
    private String postscript;

    /**
     * 是否农行 0 农行 1他行
     */
    private Integer isAbcBank;

    /**
     * 收款方开户行号
     */
    private String bankNo;


}
