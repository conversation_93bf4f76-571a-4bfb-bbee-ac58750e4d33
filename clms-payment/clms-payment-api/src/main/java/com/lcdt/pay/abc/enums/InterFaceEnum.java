package com.lcdt.pay.abc.enums;

/**
 * <AUTHOR>
 * @since 2022/3/11 18:38
 */
public enum InterFaceEnum {
    ADDMULTILEVELLEDGER("CMLT40", "增加多级账簿"),
    SINGLETOMALE("CFRT02", "单笔对公"),
    QUERYBALANCEACCOUNT("CQRA20", "查询账簿余额"),
    QUERY_ACCOUNT_INFO("CQRD02", "查询单账簿明细"),
    QUERYACCOUNTBALANCEINFO("CQRA18","查询单账户明细"),
    CHECKACCOUNTBALANCE("CQRA06", "查询账户余额"),
    BALANCE_CHANGE("CFRA01", "账簿余额调整"),
    ELECTRONIC_RECEIPT("CMRA76", "联机单笔实时查询PDF电子回单文件"),
    DRIVERWITHDRAW("CFRT02", "司机提现"),
    PAYEEWITHDRAW("CFRT02", "车队长提现"),
    FINANCIALTRANSACTIONSSTATUS("CQRT71","查询金融交易处理状态"),

    ;


    private String interfaceName;
    private String interfaceCode;

    InterFaceEnum(String interfaceCode, String interfaceName) {
        this.interfaceCode = interfaceCode;
        this.interfaceName = interfaceName;
    }

    public String getInterfaceCode() {
        return interfaceCode;
    }

    public void setInterfaceCode(String interfaceCode) {
        this.interfaceCode = interfaceCode;
    }

    public String getInterfaceName() {
        return interfaceName;
    }

    public void setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
    }
}

