package com.lcdt.pay.abc.util;

import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.Socket;

/**
 * <AUTHOR>
 * @date 2022-03-08
 */
@Slf4j
public class AbcSocketClient {

    /**
     * 发送报文并接收响应报文
     *
     * @param url
     * @param port
     * @param data
     * @return
     * @throws Exception
     */
    public static String sendAndReceive(String url, int port, String data) throws Exception {
        log.info("农行请求报文：{}", data);
        // 添加请求头信息
        data = genRequestData(data);
        Socket socket = new Socket(url, port);
        // 设置socket io读写超时时间
        socket.setSoTimeout(6000);
        OutputStream bw = socket.getOutputStream();
        bw.write(data.getBytes("gbk"));
        bw.flush();
        InputStream ips = socket.getInputStream();
        StringBuffer sb = new StringBuffer();
        int len = 0;
        byte[] buf = new byte[1024];
        while ((len = ips.read(buf)) != -1) {
            sb.append(new String(buf, 0, len, "gbk"));
        }
        bw.close();
        ips.close();
        socket.close();
        // 特殊处理，去掉报文头信息
        String msg = sb.toString().substring(7);
        log.info("农行响应报文：{}", msg);
        return msg;
    }


    /**
     * 请求数据：加密标识（1加密,0不加密） + 请求xml数据的长度（默认7位，不够补空格） + 请求的xml
     *
     * @param s
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String genRequestData(String s) throws UnsupportedEncodingException {
        return "1" + String.format("%1$-6s", s.getBytes("gbk").length) + s;
    }
}
