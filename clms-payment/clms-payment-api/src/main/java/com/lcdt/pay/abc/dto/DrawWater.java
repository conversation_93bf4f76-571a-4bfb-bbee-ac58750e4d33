package com.lcdt.pay.abc.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/4/18 18:01
 */
@Data
public class DrawWater   extends AbcBase {

    private static final long serialVersionUID = 2928902873240115281L;

    private String respSource;

    /**
     * TransSta 字典含义 解释
     * 1 待复核 表示等待复核，交易还没有发到后台，除
     * 了极少数客户提出 ERP 录入客户端复核
     * 的需求外，其他客户的 ERP 交易是不需
     * 要复核直接提交后台的，因此不会遇到这
     * 个状态
     * 2 未复核完成 同上，表示还未完全通过复核环节
     * 3 正在发送 此为临时状态，银行系统会及时更新为最
     * 终的处理状态
     * 4 处理成功 此为最终状态，即交易成功
     * 5 交易失败 此为最终状态，即交易失败
     * 6 落地待处理 交易等待柜员人工处理，人工处理完后系
     * 统会自动更新为 4（成功）或 5（失败）
     * 7 预约受理 客户使用了预约功能，系统等待到了预约
     * 时间再提交后台
     * 8 交易取消 此状态比较少见，可视为失败
     * 9 交易状态未知 需要继续查证
     * 如果 ERP 遇到 TransSta 始终为 9（交易状态未知）的情况，需要联系银行人员进行人工确
     * 认
     *
     *
     * 5 和 8 是失败， 4是成功 其他等待
     */
    private String transSta;
}
