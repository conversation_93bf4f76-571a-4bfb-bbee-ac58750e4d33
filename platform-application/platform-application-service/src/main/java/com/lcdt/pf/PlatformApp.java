package com.lcdt.pf;

import com.lcdt.common.component.RedisCache;
import com.lcdt.common.config.*;
import com.lcdt.notify.config.AliyunSmsConfig;
import com.lcdt.notify.config.ChuangLanAccountConfig;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.security.TokenService;
import com.lcdt.traffic.config.OcrConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Created by ybq on 2020/1/8 16:32
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class},
        scanBasePackages = {"com.lcdt.pf",
                "com.lcdt.depend.service",
                "com.lcdt.traffic.service",
                "com.lcdt.traffic.rpc",
                "com.lcdt.traffic.notify",
                "com.lcdt.userinfo.service",
                "com.lcdt.userinfo.rpc",
                "com.lcdt.notify.service",
                "com.lcdt.notify.rpcserviceImpl",
                "com.lcdt.pay.bkcloudfunds.rpc",
                "com.lcdt.pay.abc.service",
                "com.lcdt.common.prop",
                "com.lcdt.common.component",
                "com.lcdt.traffic.util",
                "com.lcdt.notify.websocket.service",
                "com.lcdt.notify.smsnotify",
                "com.lcdt.location",

        })
@EnableTransactionManagement
@Import({AliyunOssConfig.class, MybatisPlusConfig.class, SettingProperties.class, SecurityInfoGetter.class, TokenService.class,
        AcsConfig.class, RedisCache.class, BaiduYyConfig.class, OcrConfig.class, AliyunSmsConfig.class, ChuangLanAccountConfig.class
})
public class PlatformApp {
    public static void main(String[] args) {
        SpringApplication.run(PlatformApp.class, args);
    }
}
