<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>clms-manage</artifactId>
        <groupId>com.lcdt.cloud</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>clms-manage-service</artifactId>

    <name>clms-manage-service</name>
    <dependencies>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-manage-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>common-utils</artifactId>
            <version>1.0</version>
        </dependency>
        <!--<dependency>-->
        <!--<groupId>com.baomidou</groupId>-->
        <!--<artifactId>mybatis-plus-support</artifactId>-->
        <!--<version>2.3</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.nekohtml</groupId>
            <artifactId>nekohtml</artifactId>
            <version>1.9.21</version>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>com.lcdt.cloud</groupId>-->
            <!--<artifactId>clms-security</artifactId>-->
            <!--<version>1.0</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>com.lcdt.cloud</groupId>-->
            <!--<artifactId>common-config</artifactId>-->
            <!--<version>1.0</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
            <!--<groupId>com.lcdt.cloud</groupId>-->
            <!--<artifactId>clms-traffic-api</artifactId>-->
            <!--<version>1.0</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-response-converter</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-jsqlparser</artifactId>
        </dependency>
    </dependencies>
    <build>


        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/mapper/xml/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**</include>
                </includes>
            </resource>
        </resources>

    </build>
</project>