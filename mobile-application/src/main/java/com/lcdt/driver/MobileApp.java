package com.lcdt.driver;

import com.lcdt.common.component.RedisCache;
import com.lcdt.common.config.*;
import com.lcdt.driver.security.EnableTokenBaseSecurity;
import com.lcdt.driver.security.config.TokenBaseSecurityConfig;
import com.lcdt.notify.config.AliyunSmsConfig;
import com.lcdt.notify.config.ChuangLanAccountConfig;
import com.lcdt.security.helper.SecurityInfoGetter;
import com.lcdt.security.security.TokenService;
import com.lcdt.traffic.config.OcrConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication(
        scanBasePackages = {"com.lcdt.common.component",
                "com.lcdt.driver.**",
                "com.lcdt.depend.service",
                "com.lcdt.traffic.service",
                "com.lcdt.traffic.rpc",
                "com.lcdt.traffic.notify",
                "com.lcdt.userinfo.service",
                "com.lcdt.userinfo.rpc",
                "com.lcdt.notify.service",
                "com.lcdt.notify.rpcserviceImpl",
                "com.lcdt.pay.bkcloudfunds.rpc",
                "com.lcdt.pay.abc.service",
                "com.lcdt.common.prop",
                "com.lcdt.traffic.util",
                "com.lcdt.notify.websocket.service",
                "com.lcdt.notify.smsnotify",
                "com.lcdt.location",
        })
@EnableTokenBaseSecurity
@Import({TokenBaseSecurityConfig.class, MybatisPlusConfig.class,
        BaiduYyConfig.class, RedisConfiguration.class, RedisCache.class, SettingProperties.class, AliyunOssConfig.class,
        OcrConfig.class,
        SecurityInfoGetter.class, TokenService.class, AcsConfig.class, AliyunSmsConfig.class, ChuangLanAccountConfig.class})
public class MobileApp {

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    public static void main(String[] args) {
        SpringApplication.run(MobileApp.class, args);
    }

}
