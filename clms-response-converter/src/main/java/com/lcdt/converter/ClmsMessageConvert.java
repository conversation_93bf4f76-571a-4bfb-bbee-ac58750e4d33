package com.lcdt.converter;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by ss on 2017/10/30.
 * 
 * 自定义消息转换器，统一响应格式
 */
public class ClmsMessageConvert extends MappingJackson2HttpMessageConverter {

    /**
     * 构造函数
     * 配置ObjectMapper的相关设置
     */
    public ClmsMessageConvert() {
        super();
        ObjectMapper objectMapper = getObjectMapper();
        // 配置序列化选项
        objectMapper.configure(com.fasterxml.jackson.core.JsonGenerator.Feature.WRITE_NUMBERS_AS_STRINGS, false);
        objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_NULL_MAP_VALUES, true);
    }

    @Override
    protected void writeInternal(Object obj, HttpOutputMessage outputMessage) throws IOException, HttpMessageNotWritableException {
        Map<String, Object> successObject = new HashMap<>();
        if (obj != null) {
            successObject.put("data", obj);
        }
        successObject.put("message", "请求成功");
        successObject.put("code", 0);
        super.writeInternal(successObject, outputMessage);
    }

    @Override
    protected boolean supports(Class<?> clazz) {
        return ResponseData.class.isAssignableFrom(clazz);
    }

    @Override
    public boolean canWrite(Class<?> clazz, MediaType mediaType) {
        return supports(clazz) && super.canWrite(clazz, mediaType);
    }
}
