<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>clms-traffic</artifactId>
        <groupId>com.lcdt.cloud</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>clms-traffic-api</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-user-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-notify-api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.lcdt.cloud</groupId>
            <artifactId>clms-location-api</artifactId>
            <version>1.0</version>
        </dependency>
    </dependencies>

<!--    <build>-->
<!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-surefire-plugin</artifactId>-->

<!--            </plugin>-->
<!--        </plugins>-->
<!--    </build>-->
</project>