package com.lcdt.traffic.config;

import org.quartz.Scheduler;
import org.quartz.spi.JobFactory;
import org.quartz.spi.TriggerFiredBundle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.Properties;

/**
 * 定时任务配置
 * 使用Spring的默认数据库连接池
 */
//@Configuration
public class QuartzConfigration {

    private static final Logger logger = LoggerFactory.getLogger(QuartzConfigration.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private MyJobFactory myJobFactory;

    @Autowired
    @Qualifier("dataSource")
    private DataSource dataSource;

    /**
     * 配置SchedulerFactoryBean
     */
    @Value("${spring.quartz.auto-startup:false}")
    private boolean quartzAutoStartup;

//    @Bean
    public SchedulerFactoryBean schedulerFactoryBean() {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        try {
            // 设置数据源
            factory.setDataSource(dataSource);
            // 设置自定义JobFactory
            factory.setJobFactory(myJobFactory);
            // 设置Quartz属性
            factory.setQuartzProperties(quartzProperties());
            // 使用配置文件中的auto-startup设置
            factory.setAutoStartup(quartzAutoStartup);
            // 设置Scheduler名称
            factory.setSchedulerName("TrafficScheduler");
            // 设置覆盖已存在的Job
            factory.setOverwriteExistingJobs(true);
            // 设置启动时延迟5秒
            factory.setStartupDelay(5);
            // 设置应用上下文
            factory.setApplicationContextSchedulerContextKey("applicationContext");
            
            logger.info("Quartz Scheduler initialized with auto-startup: {}", quartzAutoStartup);
        } catch (Exception e) {
            logger.error("初始化Quartz配置失败", e);
            throw new RuntimeException("初始化Quartz配置失败", e);
        }
        return factory;
    }

    /**
     * 配置Quartz属性
     */
//    @Bean
    public Properties quartzProperties() {
        Properties properties = new Properties();
        
        // 配置调度器
        properties.setProperty("org.quartz.scheduler.instanceName", "TrafficScheduler");
        properties.setProperty("org.quartz.scheduler.instanceId", "AUTO");
        
        // 配置线程池
        properties.setProperty("org.quartz.threadPool.class", "org.quartz.simpl.SimpleThreadPool");
        properties.setProperty("org.quartz.threadPool.threadCount", "10");
        properties.setProperty("org.quartz.threadPool.threadPriority", "5");
        
        // 配置作业存储 - 使用Spring的LocalDataSourceJobStore
        properties.setProperty("org.quartz.jobStore.class", "org.springframework.scheduling.quartz.LocalDataSourceJobStore");
        properties.setProperty("org.quartz.jobStore.driverDelegateClass", "org.quartz.impl.jdbcjobstore.StdJDBCDelegate");
        properties.setProperty("org.quartz.jobStore.tablePrefix", "QRTZ_");
        properties.setProperty("org.quartz.jobStore.isClustered", "false");
        properties.setProperty("org.quartz.jobStore.useProperties", "false");
        
        // 禁用c3p0
        properties.setProperty("org.quartz.jobStore.useProperties", "true");
        properties.setProperty("org.quartz.scheduler.skipUpdateCheck", "true");
        properties.setProperty("org.quartz.jobStore.driverDelegateClass", "org.quartz.impl.jdbcjobstore.StdJDBCDelegate");
        
        return properties;
    }

    /**
     * 创建Scheduler
     */
//    @Bean(name = "scheduler")
    public Scheduler scheduler() {
        return schedulerFactoryBean().getScheduler();
    }
    
    /**
     * 配置JobFactory
     */
//    @Bean
    public JobFactory jobFactory(ApplicationContext applicationContext) {
        AutowiringSpringBeanJobFactory jobFactory = new AutowiringSpringBeanJobFactory();
        jobFactory.setApplicationContext(applicationContext);
        return jobFactory;
    }
}

/**
 * 自定义JobFactory，用于解决Spring Bean注入问题
 */
class AutowiringSpringBeanJobFactory extends org.springframework.scheduling.quartz.SpringBeanJobFactory {
    
    private AutowireCapableBeanFactory beanFactory;

    @Override
    public void setApplicationContext(final ApplicationContext context) {
        beanFactory = context.getAutowireCapableBeanFactory();
    }

    @Override
    protected Object createJobInstance(final TriggerFiredBundle bundle) throws Exception {
        final Object job = super.createJobInstance(bundle);
        beanFactory.autowireBean(job);
        return job;
    }
}
