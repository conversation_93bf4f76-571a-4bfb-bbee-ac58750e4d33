package com.lcdt.security.config;

import com.lcdt.security.filter.JwtAuthenticationTokenFilter;
import com.lcdt.security.filter.JwtSysLogFilter;
import com.lcdt.security.filter.SmsAuthenticationTokenFilter;
import com.lcdt.security.security.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.expression.WebExpressionAuthorizationManager;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.util.AntPathMatcher;

import java.util.ArrayList;

/**
 * Created by ss on 2017/8/8.
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(jsr250Enabled = true, prePostEnabled = true)
//@ComponentScan({"com.lcdt.clms.security.config", "com.lcdt.clms.security.auth"})
public class SecurityConfiguration {

    private Logger logger = LoggerFactory.getLogger(SecurityConfiguration.class);

    /**
     * 认证失败处理类
     */
    @Autowired
    private AuthenticationEntryPointImpl unauthorizedHandler;

    /**
     * 权限不足处理
     */
    @Autowired
    private RestAccessDeniedHandler restAccessDeniedHandler;

    /**
     * token认证过滤器
     */
    @Autowired
    private JwtAuthenticationTokenFilter authenticationTokenFilter;

    @Autowired
    private SmsAuthenticationTokenFilter smsAuthenticationTokenFilter;

    @Autowired
    private JwtSysLogFilter jwtSysLogFilter;

    @Autowired
    private SmsAuthenticationProvider smsAuthenticationProvider;

    @Autowired
    private UsernamePasswordAuthenticationProvider usernamePasswordAuthenticationProvider;


    /**
     * 退出处理类
     */
    @Autowired
    private LogoutSuccessHandlerImpl logoutSuccessHandler;


    private AnnotationAttributes enableClmsSecurity;

    /**
     * Web安全自定义配置
     * Spring Security 6.x推荐完全移除web.ignoring()的使用
     * 所有路径都应该通过HttpSecurity.authorizeHttpRequests配置
     * 注释掉WebSecurityCustomizer以消除所有警告
     */
    // @Bean
    // public WebSecurityCustomizer webSecurityCustomizer() {
    //     return (web) -> {
    //         // Spring Security 6.x不推荐使用web.ignoring()
    //         // 所有路径都应该使用permitAll()
    //     };
    // }

    /**
     * 认证管理器配置
     * 配置认证提供者
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }
    
    /**
     * 认证提供者管理器配置
     * 配置自定义的认证提供者
     */
    @Bean
    public ProviderManager providerManager() {
        return new ProviderManager(usernamePasswordAuthenticationProvider, smsAuthenticationProvider);
    }

    /**
     * 安全过滤器链配置
     * 配置HTTP安全策略、认证授权规则等
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                // 禁用CSRF
                .csrf(csrf -> csrf.disable())
                // 配置异常处理
                .exceptionHandling(exception -> exception
                        .authenticationEntryPoint(unauthorizedHandler)
                        .accessDeniedHandler(restAccessDeniedHandler))
                // 配置登出
                .logout(logout -> logout
                        .logoutUrl("/logout")
                        .logoutSuccessHandler(logoutSuccessHandler))
                // 使用JWT，关闭session
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                // 配置请求授权 - 按照从具体到通用的顺序配置
                .authorizeHttpRequests(auth -> auth
                        // 1. 静态资源 - 最高优先级，避免被其他规则拦截
                        .requestMatchers("/favicon.ico").permitAll()
                        .requestMatchers("/asset/**", "/css/**", "/js/**", "/img/**").permitAll()
                        .requestMatchers(HttpMethod.GET,
                                "/*.html",
                                "/**/*.html",
                                "/**/*.css",
                                "/**/*.js",
                                "/**/*.png",
                                "/**/*.jpg",
                                "/**/*.jpeg",
                                "/**/*.gif",
                                "/**/*.ico").permitAll()

                        // 2. 系统级别的公开资源
                        .requestMatchers("/glCmKnzla9.txt").permitAll()

                        // 3. 认证相关接口 - 完全公开
                        .requestMatchers("/auth/**", "/carrier/login", "/shipper/login", "/wechat/login",
                                "/wechat/sms-login", "/wechat/send-sms", "/captchaImage", "/carrier/sendcode", "/carrier/checkCode").permitAll()

                        // 4. 密码重置相关接口
                        .requestMatchers("/forgetPassword", "/forgetpwd/**").permitAll()

                        // 5. 账户和客户绑定相关接口
                        .requestMatchers("/account/**", "/customer/bind/**").permitAll()

                        // 6. 第三方回调接口 - 支付、短信等
                        .requestMatchers("/wechatpaynotify", "/alipay/notify", "/alipay/returnurl", "/smsreturn").permitAll()

                        // 7. 设备和位置相关回调
                        .requestMatchers("/location/receivewarning").permitAll()

                        // 8. 注册相关接口
                        .requestMatchers("/register/**").permitAll()

                        // 9. 开放API接口
                        .requestMatchers("/open/api/**").permitAll()

                        // 10. 云服务回调接口
                        .requestMatchers("/cloudnotify/**", "/cloudfunds/**", "/antfinance/**").permitAll()

                        // 11. OSS代理接口
                        .requestMatchers("/oss/proxy/**").permitAll()

                        // 12. 健康检查接口
                        .requestMatchers("/actuator/health/readiness", "/actuator/health/liveness").permitAll()

                        // 13. 文章接口
                        .requestMatchers("/articles/list", "/articles/detail").permitAll()

                        // 14. 其他所有请求通过RBAC权限校验
                        .requestMatchers("/**").access(new WebExpressionAuthorizationManager("@rbacService.hasPermission(request,authentication)"))

                        // 15. 剩余请求需要认证（作为兜底规则）
                        .anyRequest().authenticated())
                // 添加自定义过滤器
                .addFilterBefore(authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class)
                .addFilterBefore(smsAuthenticationTokenFilter, UsernamePasswordAuthenticationFilter.class)
                .addFilterBefore(jwtSysLogFilter, UsernamePasswordAuthenticationFilter.class)
                // 配置frame选项
                .headers(headers -> headers.frameOptions().sameOrigin());

        return http.build();
    }

    @Bean
    public SessionRegistry sessionRegistry() {
        return new SessionRegistryImpl();
    }

//    @Bean
//    public TicketLogoutSuccessHandler ticketLogoutSuccessHandler() {
//        return new TicketLogoutSuccessHandler();
//    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 实例化spring的一个工具类
     *
     * @return
     */
    @Bean
    public AntPathMatcher antPathMatcher() {
        return new AntPathMatcher();
    }

    /* @Bean
     public CasLoginEntryPoint entryPoint() {
         return new CasLoginEntryPoint();
     }*/
/*    public TicketAuthenticationFilter ticketAuthenticationFilter() {
        TicketAuthenticationFilter ticketAuthenticationFilter = new TicketAuthenticationFilter(new CookieTicketProvider(), ticketManager);
        ticketAuthenticationFilter.setAuthenticationFailureHandler(new TicketAuthFailureHandler());
        try {
            ticketAuthenticationFilter.setAuthenticationManager(authenticationManager());
        } catch (Exception e) {
            logger.error("设置认证管理器错误", e);
        }
        return ticketAuthenticationFilter;
    }*/

//	@Bean
//	public TicketAccessDeniedHandler deniedHandler() {
//		return new TicketAccessDeniedHandler();
//	}

//	@Bean
//	public LocaleResolver localeResolver() {
//		SessionLocaleResolver sessionLocaleResolver = new SessionLocaleResolver();
//		sessionLocaleResolver.setDefaultLocale(Locale.CHINA);
//		return sessionLocaleResolver;
//	}

    /**
     * 重新实例化bean AuthenticationManager
     * 这里实例化是为了解决AuthenticationManager在spring容器中找不到对应的bean的问题
     */
/*    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();

    }*/


}
